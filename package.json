{"name": "geek-assistant", "version": "1.0.9", "description": "Geek 助手 - AI伙伴", "main": "out/main/index.js", "scripts": {"dev": "electron-vite dev --config electron.vite.config.ts", "build": "electron-vite build --config electron.vite.config.ts", "preview": "electron-vite preview --config electron.vite.config.ts", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "release": "node scripts/release.js", "clean": "rm -rf out dist-electron node_modules/.cache", "lint": "echo '<PERSON><PERSON> skipped'", "start": "npm run dev"}, "keywords": ["electron", "interview", "ai", "assistant"], "author": "Geek Assistant Team", "license": "MIT", "type": "module", "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@deepgram/sdk": "^4.11.0", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@google/genai": "^1.9.0", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.50.4", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-vite": "^4.0.0", "groq-sdk": "^0.29.0", "lucide-react": "^0.525.0", "openai": "^5.10.2", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "together-ai": "^0.21.0", "typescript": "^5.8.3", "vite": "^7.0.4"}, "devDependencies": {"@playwright/test": "^1.54.0", "electron": "^37.2.0", "electron-builder": "^26.0.12"}, "build": {"appId": "com.geekassistant.app", "productName": "GeekAssistant", "directories": {"output": "dist-electron"}, "files": ["out/**/*", "resources/**/*", "assets/**/*"], "mac": {"icon": "build/icon.icns", "category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64"]}]}, "win": {"icon": "resources/icon.png", "target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"icon": "resources/icon.png", "target": [{"target": "AppImage", "arch": ["x64"]}]}}}