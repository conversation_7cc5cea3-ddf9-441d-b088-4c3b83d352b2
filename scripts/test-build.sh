#!/bin/bash

# 🔧 GeekAssistant 打包测试脚本
# 用于快速测试环境变量修复效果

echo "🚀 GeekAssistant 打包测试开始..."

# 检查环境变量文件
echo ""
echo "📋 检查环境变量配置..."
if [ -f ".env.local" ]; then
    echo "✅ .env.local 文件存在"
    echo "🔑 API密钥配置状态:"
    
    # 检查各个API密钥
    if grep -q "VITE_GEMINI_API_KEY=" .env.local && ! grep -q "VITE_GEMINI_API_KEY=your_" .env.local; then
        echo "  ✅ Gemini API密钥: 已配置"
    else
        echo "  ❌ Gemini API密钥: 未配置或使用默认值"
    fi
    
    if grep -q "VITE_ASSEMBLYAI_API_KEY=" .env.local && ! grep -q "VITE_ASSEMBLYAI_API_KEY=your_" .env.local; then
        echo "  ✅ AssemblyAI API密钥: 已配置"
    else
        echo "  ⚠️  AssemblyAI API密钥: 未配置"
    fi
    
    if grep -q "VITE_DEEPGRAM_API_KEY=" .env.local && ! grep -q "VITE_DEEPGRAM_API_KEY=your_" .env.local; then
        echo "  ✅ Deepgram API密钥: 已配置"
    else
        echo "  ⚠️  Deepgram API密钥: 未配置"
    fi
    
    if grep -q "VITE_GROQ_API_KEY=" .env.local && ! grep -q "VITE_GROQ_API_KEY=your_" .env.local; then
        echo "  ✅ Groq API密钥: 已配置"
    else
        echo "  ⚠️  Groq API密钥: 未配置"
    fi
else
    echo "❌ .env.local 文件不存在！请先创建并配置API密钥"
    echo "💡 运行: cp .env.example .env.local"
    exit 1
fi

echo ""
echo "🔨 开始构建应用..."

# 清理之前的构建
echo "🧹 清理旧的构建文件..."
rm -rf out dist-electron

# 构建应用
echo "⚙️  正在构建..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
else
    echo "❌ 构建失败！请检查错误信息"
    exit 1
fi

# 打包应用
echo ""
echo "📦 开始打包 macOS 应用..."
npm run build:mac

if [ $? -eq 0 ]; then
    echo "✅ 打包成功！"
    echo ""
    echo "🎉 测试完成！应用已打包到 dist-electron/ 目录"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 打开 dist-electron/ 目录中的 .dmg 文件"
    echo "2. 安装应用到 Applications 文件夹"
    echo "3. 启动应用并测试转录和AI功能"
    echo "4. 如果仍有问题，点击应用中的 '🔍 调试' 按钮查看详细信息"
    echo ""
    echo "🔍 调试提示:"
    echo "- 在应用中按 Cmd+Option+I 打开开发者工具查看控制台日志"
    echo "- 使用调试面板检查环境变量是否正确加载"
    echo "- 确保网络连接正常，某些企业网络可能阻止API请求"
else
    echo "❌ 打包失败！请检查错误信息"
    exit 1
fi
