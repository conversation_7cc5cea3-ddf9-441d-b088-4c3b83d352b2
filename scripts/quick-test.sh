#!/bin/bash

# 🔧 GeekAssistant 快速测试脚本
# 用于快速测试修复后的调试面板

echo "🚀 GeekAssistant 快速测试开始..."

# 检查构建文件是否存在
if [ ! -d "out" ]; then
    echo "❌ 构建文件不存在，请先运行 npm run build"
    exit 1
fi

echo "✅ 构建文件存在"

# 启动开发模式进行测试
echo "🔍 启动开发模式测试..."
echo ""
echo "📋 测试步骤:"
echo "1. 应用启动后，立即点击右下角的 '🔍 调试' 按钮"
echo "2. 检查调试面板是否正常显示，没有错误"
echo "3. 观察实时日志窗口是否有初始化信息"
echo "4. 点击 '🎤 测试音频' 和 '🤖 测试Gemini' 按钮"
echo "5. 进入协作模式，观察日志中是否有转录数据"
echo ""
echo "🔍 如果调试面板出现错误，请按 Cmd+Option+I 查看控制台"
echo ""

# 启动应用
npm run dev
