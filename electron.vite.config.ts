import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import { loadEnv } from 'vite'

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, resolve(__dirname), '')

  return {
    main: {
      plugins: [externalizeDepsPlugin()],
      build: {
        rollupOptions: {
          external: [
            'microsoft-cognitiveservices-speech-sdk',
            'ws',
            '@types/ws',
            'electron',
            'path',
            'fs',
            'os',
            'child_process'
          ]
        },
        // 优化构建性能
        minify: 'esbuild',
        sourcemap: false,
        // 减少包体积
        chunkSizeWarningLimit: 1000
      },
      define: {
        // 🔑 主要AI服务API密钥
        'process.env.VITE_GEMINI_API_KEY': JSON.stringify(env.VITE_GEMINI_API_KEY),

        // 🎤 语音转录服务API密钥
        'process.env.VITE_ASSEMBLYAI_API_KEY': JSON.stringify(env.VITE_ASSEMBLYAI_API_KEY),
        'process.env.VITE_DEEPGRAM_API_KEY': JSON.stringify(env.VITE_DEEPGRAM_API_KEY),
        'process.env.VITE_GOOGLE_SPEECH_API_KEY': JSON.stringify(env.VITE_GOOGLE_SPEECH_API_KEY),

        // 🤖 AI对话服务API密钥
        'process.env.VITE_GROQ_API_KEY': JSON.stringify(env.VITE_GROQ_API_KEY),
        'process.env.VITE_TOGETHER_API_KEY': JSON.stringify(env.VITE_TOGETHER_API_KEY),
        'process.env.VITE_OPENAI_API_KEY': JSON.stringify(env.VITE_OPENAI_API_KEY),

        // 🗄️ 数据库配置
        'process.env.VITE_SUPABASE_URL': JSON.stringify(env.VITE_SUPABASE_URL),
        'process.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(env.VITE_SUPABASE_ANON_KEY),

        // 🛠️ 系统配置
        'process.env.VITE_DEV_MODE': JSON.stringify(env.VITE_DEV_MODE),
        'process.env.VITE_API_TIMEOUT': JSON.stringify(env.VITE_API_TIMEOUT)
      }
    },
    preload: {
      plugins: [externalizeDepsPlugin()],
      define: {
        // 🔑 主要AI服务API密钥
        'process.env.VITE_GEMINI_API_KEY': JSON.stringify(env.VITE_GEMINI_API_KEY),

        // 🎤 语音转录服务API密钥
        'process.env.VITE_ASSEMBLYAI_API_KEY': JSON.stringify(env.VITE_ASSEMBLYAI_API_KEY),
        'process.env.VITE_DEEPGRAM_API_KEY': JSON.stringify(env.VITE_DEEPGRAM_API_KEY),
        'process.env.VITE_GOOGLE_SPEECH_API_KEY': JSON.stringify(env.VITE_GOOGLE_SPEECH_API_KEY),

        // 🤖 AI对话服务API密钥
        'process.env.VITE_GROQ_API_KEY': JSON.stringify(env.VITE_GROQ_API_KEY),
        'process.env.VITE_TOGETHER_API_KEY': JSON.stringify(env.VITE_TOGETHER_API_KEY),
        'process.env.VITE_OPENAI_API_KEY': JSON.stringify(env.VITE_OPENAI_API_KEY),

        // 🗄️ 数据库配置
        'process.env.VITE_SUPABASE_URL': JSON.stringify(env.VITE_SUPABASE_URL),
        'process.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(env.VITE_SUPABASE_ANON_KEY),

        // 🛠️ 系统配置
        'process.env.VITE_DEV_MODE': JSON.stringify(env.VITE_DEV_MODE),
        'process.env.VITE_API_TIMEOUT': JSON.stringify(env.VITE_API_TIMEOUT)
      }
    },
    renderer: {
      resolve: {
        alias: {
          '@renderer': resolve(__dirname, './src/renderer/src'),
          '@': resolve(__dirname, './src/renderer/src')
        }
      },
      plugins: [react(), tailwindcss()],
      build: {
        // 优化构建性能和包体积
        minify: 'esbuild',
        sourcemap: false,
        rollupOptions: {
          output: {
            // 更细粒度的代码分割优化
            manualChunks: {
              'react-vendor': ['react', 'react-dom'],
              'router-vendor': ['react-router-dom'],
              'ui-vendor': ['lucide-react', '@radix-ui/react-slot', 'class-variance-authority', 'clsx'],
              'ai-vendor': ['@google/genai', 'groq-sdk', 'openai', '@anthropic-ai/sdk'],
              'utils': ['./src/renderer/src/utils/LocalStorageCache', './src/renderer/src/utils/EventListenerManager'],
              'audio': ['./src/renderer/src/utils/AudioProcessor']
            }
          }
        },
        chunkSizeWarningLimit: 1000
      },
      define: {
        // 🔑 主要AI服务API密钥
        'process.env.VITE_GEMINI_API_KEY': JSON.stringify(env.VITE_GEMINI_API_KEY),

        // 🎤 语音转录服务API密钥
        'process.env.VITE_ASSEMBLYAI_API_KEY': JSON.stringify(env.VITE_ASSEMBLYAI_API_KEY),
        'process.env.VITE_DEEPGRAM_API_KEY': JSON.stringify(env.VITE_DEEPGRAM_API_KEY),
        'process.env.VITE_GOOGLE_SPEECH_API_KEY': JSON.stringify(env.VITE_GOOGLE_SPEECH_API_KEY),

        // 🤖 AI对话服务API密钥
        'process.env.VITE_GROQ_API_KEY': JSON.stringify(env.VITE_GROQ_API_KEY),
        'process.env.VITE_TOGETHER_API_KEY': JSON.stringify(env.VITE_TOGETHER_API_KEY),
        'process.env.VITE_OPENAI_API_KEY': JSON.stringify(env.VITE_OPENAI_API_KEY),

        // 🗄️ 数据库配置
        'process.env.VITE_SUPABASE_URL': JSON.stringify(env.VITE_SUPABASE_URL),
        'process.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(env.VITE_SUPABASE_ANON_KEY),

        // 🛠️ 系统配置
        'process.env.VITE_DEV_MODE': JSON.stringify(env.VITE_DEV_MODE),
        'process.env.VITE_API_TIMEOUT': JSON.stringify(env.VITE_API_TIMEOUT)
      }
    }
  }
})
