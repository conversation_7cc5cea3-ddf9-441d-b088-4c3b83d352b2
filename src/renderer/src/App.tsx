import React, { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom'
import WelcomePage from './components/WelcomePage'
import MainPage from './components/MainPage'
import CollaborationMode from './components/CollaborationMode'
import MeetingMode from './components/MeetingMode'
import DebugPanel from './components/DebugPanel'
import { getDefaultPreparations } from './data/defaultPreparations'
import { cacheUtils } from './utils/LocalStorageCache'
import {
  CreatePreparationWrapper,
  FloatingWindowWrapper,
  TestPageWrapper,
  preloadCriticalComponents,
  preloadByUserAction
} from './components/LazyComponents'

// 声明全局类型
declare global {
  interface Window {
    geekAssistant: {
      createFloatingWindow: () => Promise<boolean>
      closeFloatingWindow: () => Promise<boolean>
      enterCollaborationMode: () => Promise<boolean>
      exitCollaborationMode: () => Promise<boolean>
      initializeGemini: (apiKey: string, customPrompt?: string, profile?: string, language?: string) => Promise<boolean>
      startAudioCapture: () => Promise<boolean>
      stopAudioCapture: () => Promise<boolean>
      reconnectGemini: () => Promise<boolean>
      manualReconnect: () => Promise<boolean>
      disconnectGemini: () => Promise<boolean>

      // 权限管理
      checkPermissions: () => Promise<any>
      checkScreenRecordingPermission: () => Promise<any>
      checkMicrophonePermission: () => Promise<any>
      checkApiKeyStatus: () => Promise<any>
      checkAudioDeviceStatus: () => Promise<any>
      openSystemPreferences: (pane: string) => Promise<boolean>
      testAudioCapture: () => Promise<any>
      requestMicrophonePermission: () => Promise<any>

      // 服务管理
      getServiceStatus: () => Promise<any>
      getCurrentServiceConfig: () => Promise<any>
      switchService: (config: any) => Promise<boolean>
      updateServiceConfig: (config: any) => Promise<boolean>

      // 队列管理
      getQueueStatus: () => Promise<any>
      clearAllQueues: () => Promise<boolean>
      restartStreamManager: () => Promise<boolean>

      onStatusUpdate: (callback: (status: string) => void) => () => void
      onTranscriptionUpdate: (callback: (text: string) => void) => () => void
      onAIResponse: (callback: (response: string) => void) => () => void
      onSessionInitializing: (callback: (initializing: boolean) => void) => () => void
      onSessionError: (callback: (error: string) => void) => () => void
      onSessionClosed: (callback: () => void) => () => void
      onQueueMetrics?: (callback: (metrics: any) => void) => () => void
    }
    env: {
      GEMINI_API_KEY?: string
      SUPABASE_URL?: string
      SUPABASE_ANON_KEY?: string
      DEV_MODE?: string
    }
  }
}

// 协作模式包装组件
const CollaborationModeWrapper: React.FC = () => {
  const navigate = useNavigate()

  const handleExit = () => {
    navigate('/')
  }

  return <CollaborationMode onBack={handleExit} />
}

// 会议模式包装组件
const MeetingModeWrapper: React.FC = () => {
  const navigate = useNavigate()

  const handleExit = () => {
    navigate('/')
  }

  return <MeetingMode onExit={handleExit} />
}

function App() {
  const [isFirstTime, setIsFirstTime] = useState(true)
  const [preparations, setPreparations] = useState<any[]>([])

  useEffect(() => {
    // 🐾💾 使用缓存系统优化localStorage读写（第1步优化）

    const hasVisited = cacheUtils.getHasVisited()
    const hasPreparations = cacheUtils.get('geek-assistant-preparations', () => {
      const stored = localStorage.getItem('geek-assistant-preparations')
      return stored
    })


    // 🐾📦 懒加载优化：预加载关键组件（第8步优化）
    preloadCriticalComponents().then(() => {
    })

    // 临时强制显示欢迎页面，忽略访问标识
    if (false && hasVisited) {
      // 不是首次访问，直接进入主页
      setIsFirstTime(false)

      // 🐾📦 预加载用户可能使用的组件（第8步优化）
      preloadByUserAction('returning_user')

      if (hasPreparations) {
        try {
          // 🐾🔄 第9步优化：安全的数据解析
          let existingPreparations
          if (typeof hasPreparations === 'string') {
            existingPreparations = JSON.parse(hasPreparations)
          } else if (Array.isArray(hasPreparations)) {
            existingPreparations = hasPreparations
          } else {
            throw new Error('Invalid preparations data type')
          }

          setPreparations(existingPreparations)
        } catch (error) {
          const defaultPreps = getDefaultPreparations()
          setPreparations(defaultPreps)
          cacheUtils.set('geek-assistant-preparations', JSON.stringify(defaultPreps))
        }
      } else {
        // 如果访问过但没有准备项目，加载默认数据
        const defaultPreps = getDefaultPreparations()
        setPreparations(defaultPreps)
        cacheUtils.set('geek-assistant-preparations', JSON.stringify(defaultPreps))
      }
    } else {
      // 首次访问，预加载默认数据但仍显示欢迎页面
      const defaultPreps = getDefaultPreparations()
      setPreparations(defaultPreps)
      cacheUtils.set('geek-assistant-preparations', JSON.stringify(defaultPreps))

      // 🐾📦 首次用户预加载新手引导相关组件（第8步优化）
      preloadByUserAction('first_time_user')

      // isFirstTime 保持为 true，显示欢迎页面
    }
  }, [])

  // 检查当前路由是否是悬浮窗
  const isFloatingWindow = window.location.hash === '#/floating'

  if (isFloatingWindow) {
    return <FloatingWindowWrapper />
  }

  return (
    <Router>
      <div className="min-h-screen bg-vercel-white">
        <Routes>
          <Route
            path="/"
            element={
              isFirstTime ? (
                <WelcomePage onComplete={(asGuest = false) => {
                  setIsFirstTime(false)
                  // 🐾💾 使用缓存系统保存访问标识（第1步优化）
                  if (!asGuest) {
                    cacheUtils.setHasVisited('true')
                  }
                }} />
              ) : (
                <MainPage preparations={preparations} setPreparations={setPreparations} />
              )
            }
          />
          <Route
            path="/collaboration"
            element={<CollaborationModeWrapper />}
          />
          <Route
            path="/meeting"
            element={<MeetingModeWrapper />}
          />
          {/* 🐾📦 使用懒加载组件（第8步优化） */}
          <Route
            path="/create-preparation"
            element={
              <CreatePreparationWrapper
                preparations={preparations}
                setPreparations={setPreparations}
              />
            }
          />
          <Route
            path="/edit-preparation/:id"
            element={
              <CreatePreparationWrapper
                preparations={preparations}
                setPreparations={setPreparations}
              />
            }
          />
          <Route path="/test" element={<TestPageWrapper />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>

        {/* 🔍 调试面板 - 仅在非生产环境或需要调试时显示 */}
        <DebugPanel />
      </div>
    </Router>
  )
}

export default App
