/**
 * 🔍 调试面板组件
 * 用于在打包后的应用中检查环境变量和服务状态
 */

import React, { useState, useEffect } from 'react'
import { AlertCircle, CheckCircle, XCircle, Eye, EyeOff } from 'lucide-react'

interface ServiceStatus {
  name: string
  status: 'connected' | 'error' | 'unknown'
  apiKey: string | undefined
  message?: string
}

interface SystemStatus {
  audioCapture: 'active' | 'inactive' | 'error' | 'unknown'
  geminiConnection: 'connected' | 'disconnected' | 'connecting' | 'error'
  transcriptionService: 'active' | 'inactive' | 'error' | 'unknown'
  lastTranscription: string
  lastAIResponse: string
  apiCallCount: number
  errorCount: number
}

export const DebugPanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [showApiKeys, setShowApiKeys] = useState(false)
  const [services, setServices] = useState<ServiceStatus[]>([])
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    audioCapture: 'unknown',
    geminiConnection: 'disconnected',
    transcriptionService: 'unknown',
    lastTranscription: '暂无',
    lastAIResponse: '暂无',
    apiCallCount: 0,
    errorCount: 0
  })
  const [logs, setLogs] = useState<string[]>([])

  useEffect(() => {
    checkServices()
    startRealTimeMonitoring()
  }, [])

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)])
  }

  const startRealTimeMonitoring = () => {
    addLog('🔍 开始实时监控...')

    // 监听转录更新
    if (window.geekAssistant?.onTranscriptionUpdate) {
      window.geekAssistant.onTranscriptionUpdate((data: string) => {
        addLog(`📝 收到转录: ${data.substring(0, 50)}...`)
        setSystemStatus(prev => ({
          ...prev,
          lastTranscription: data,
          transcriptionService: 'active'
        }))
      })
    }

    // 监听AI回复
    if (window.geekAssistant?.onAIResponse) {
      window.geekAssistant.onAIResponse((data: string) => {
        addLog(`🤖 收到AI回复: ${data.substring(0, 50)}...`)
        setSystemStatus(prev => ({
          ...prev,
          lastAIResponse: data
        }))
      })
    }

    // 定期检查系统状态
    const statusInterval = setInterval(async () => {
      try {
        if (window.geekAssistant?.getServiceStatus) {
          const status = await window.geekAssistant.getServiceStatus()
          setSystemStatus(prev => ({
            ...prev,
            audioCapture: status.audioCapture || 'unknown',
            geminiConnection: status.geminiConnection || 'disconnected'
          }))
        }
      } catch (error) {
        addLog(`❌ 状态检查失败: ${error}`)
      }
    }, 2000)

    return () => clearInterval(statusInterval)
  }

  const checkServices = () => {
    const serviceList: ServiceStatus[] = [
      {
        name: 'Gemini API',
        status: process.env.VITE_GEMINI_API_KEY ? 'connected' : 'error',
        apiKey: process.env.VITE_GEMINI_API_KEY,
        message: process.env.VITE_GEMINI_API_KEY ? '✅ API密钥已配置' : '❌ 缺少API密钥'
      },
      {
        name: 'AssemblyAI',
        status: process.env.VITE_ASSEMBLYAI_API_KEY ? 'connected' : 'error',
        apiKey: process.env.VITE_ASSEMBLYAI_API_KEY,
        message: process.env.VITE_ASSEMBLYAI_API_KEY ? '✅ API密钥已配置' : '❌ 缺少API密钥'
      },
      {
        name: 'Deepgram',
        status: process.env.VITE_DEEPGRAM_API_KEY ? 'connected' : 'error',
        apiKey: process.env.VITE_DEEPGRAM_API_KEY,
        message: process.env.VITE_DEEPGRAM_API_KEY ? '✅ API密钥已配置' : '❌ 缺少API密钥'
      },
      {
        name: 'Groq',
        status: process.env.VITE_GROQ_API_KEY ? 'connected' : 'error',
        apiKey: process.env.VITE_GROQ_API_KEY,
        message: process.env.VITE_GROQ_API_KEY ? '✅ API密钥已配置' : '❌ 缺少API密钥'
      },
      {
        name: 'Google Speech',
        status: process.env.VITE_GOOGLE_SPEECH_API_KEY ? 'connected' : 'unknown',
        apiKey: process.env.VITE_GOOGLE_SPEECH_API_KEY,
        message: process.env.VITE_GOOGLE_SPEECH_API_KEY ? '✅ API密钥已配置' : '⚠️ 可选服务'
      }
    ]
    setServices(serviceList)
    addLog('🔄 服务状态已刷新')
  }

  const maskApiKey = (key: string | undefined) => {
    if (!key) return '未配置'
    if (key.length <= 10) return key
    return `${key.substring(0, 6)}...${key.substring(key.length - 4)}`
  }

  const testAudioCapture = async () => {
    addLog('🎤 测试音频捕获...')
    try {
      if (window.geekAssistant?.testAudioCapture) {
        const result = await window.geekAssistant.testAudioCapture()
        addLog(`🎤 音频测试结果: ${JSON.stringify(result)}`)
      } else {
        addLog('❌ 音频测试功能不可用')
      }
    } catch (error) {
      addLog(`❌ 音频测试失败: ${error}`)
    }
  }

  const testGeminiConnection = async () => {
    addLog('🤖 测试Gemini连接...')
    try {
      if (window.geekAssistant?.initializeGemini) {
        const apiKey = process.env.VITE_GEMINI_API_KEY
        if (apiKey) {
          const result = await window.geekAssistant.initializeGemini(apiKey, '', 'interview', 'zh')
          addLog(`🤖 Gemini连接结果: ${result ? '成功' : '失败'}`)
        } else {
          addLog('❌ Gemini API密钥未配置')
        }
      } else {
        addLog('❌ Gemini连接功能不可用')
      }
    } catch (error) {
      addLog(`❌ Gemini连接测试失败: ${error}`)
    }
  }

  const getStatusIcon = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-yellow-500" />
    }
  }

  const getSystemStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'connected':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'connecting':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg z-50"
        title="打开调试面板"
      >
        🔍 调试
      </button>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-800">🔍 系统调试面板</h2>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700 text-xl"
            >
              ×
            </button>
          </div>

          {/* 实时系统状态 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-700">📊 实时系统状态</h3>
            <div className="bg-gray-50 p-4 rounded-lg space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>音频捕获:</strong>
                  <span className={`ml-2 ${getSystemStatusColor(systemStatus.audioCapture)}`}>
                    {systemStatus.audioCapture}
                  </span>
                </div>
                <div>
                  <strong>Gemini连接:</strong>
                  <span className={`ml-2 ${getSystemStatusColor(systemStatus.geminiConnection)}`}>
                    {systemStatus.geminiConnection}
                  </span>
                </div>
                <div>
                  <strong>转录服务:</strong>
                  <span className={`ml-2 ${getSystemStatusColor(systemStatus.transcriptionService)}`}>
                    {systemStatus.transcriptionService}
                  </span>
                </div>
                <div>
                  <strong>API调用:</strong>
                  <span className="ml-2 text-blue-600">{systemStatus.apiCallCount}</span>
                </div>
              </div>

              <div className="border-t pt-3 space-y-2">
                <div>
                  <strong>最后转录:</strong>
                  <div className="text-xs text-gray-600 mt-1 p-2 bg-white rounded border">
                    {systemStatus.lastTranscription}
                  </div>
                </div>
                <div>
                  <strong>最后AI回复:</strong>
                  <div className="text-xs text-gray-600 mt-1 p-2 bg-white rounded border">
                    {systemStatus.lastAIResponse}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 环境信息 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-700">🔧 环境信息</h3>
            <div className="bg-gray-50 p-4 rounded-lg space-y-2 text-sm">
              <div><strong>开发模式:</strong> {process.env.VITE_DEV_MODE || 'false'}</div>
              <div><strong>API超时:</strong> {process.env.VITE_API_TIMEOUT || '30000'}ms</div>
              <div><strong>构建时间:</strong> {new Date().toLocaleString()}</div>
            </div>
          </div>

          {/* 服务状态 */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-semibold text-gray-700">🔑 API服务状态</h3>
              <button
                onClick={() => setShowApiKeys(!showApiKeys)}
                className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800"
              >
                {showApiKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {showApiKeys ? '隐藏' : '显示'} API密钥
              </button>
            </div>
            
            <div className="space-y-3">
              {services.map((service, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(service.status)}
                    <div>
                      <div className="font-medium text-gray-800">{service.name}</div>
                      <div className="text-sm text-gray-600">{service.message}</div>
                    </div>
                  </div>
                  {showApiKeys && (
                    <div className="text-xs text-gray-500 font-mono">
                      {maskApiKey(service.apiKey)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 测试功能 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-700">🧪 功能测试</h3>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={testAudioCapture}
                className="bg-green-500 hover:bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium"
              >
                🎤 测试音频
              </button>
              <button
                onClick={testGeminiConnection}
                className="bg-purple-500 hover:bg-purple-600 text-white py-2 px-3 rounded-lg text-sm font-medium"
              >
                🤖 测试Gemini
              </button>
            </div>
          </div>

          {/* 实时日志 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-700">📝 实时日志</h3>
            <div className="bg-black text-green-400 p-3 rounded-lg h-32 overflow-y-auto text-xs font-mono">
              {logs.length === 0 ? (
                <div className="text-gray-500">等待日志...</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">{log}</div>
                ))
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-3">
            <button
              onClick={checkServices}
              className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg font-medium"
            >
              🔄 刷新状态
            </button>
            <button
              onClick={() => {
                console.log('=== GeekAssistant Debug Info ===')
                console.log('Environment Variables:', {
                  VITE_GEMINI_API_KEY: !!process.env.VITE_GEMINI_API_KEY,
                  VITE_ASSEMBLYAI_API_KEY: !!process.env.VITE_ASSEMBLYAI_API_KEY,
                  VITE_DEEPGRAM_API_KEY: !!process.env.VITE_DEEPGRAM_API_KEY,
                  VITE_GROQ_API_KEY: !!process.env.VITE_GROQ_API_KEY
                })
                console.log('Services:', services)
                console.log('System Status:', systemStatus)
                console.log('Logs:', logs)
                alert('调试信息已输出到控制台')
              }}
              className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg font-medium"
            >
              📋 导出日志
            </button>
            <button
              onClick={() => setLogs([])}
              className="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg font-medium"
            >
              🗑️ 清空
            </button>
          </div>

          {/* 帮助信息 */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">💡 故障排除提示</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 如果API密钥显示"未配置"，请检查 .env.local 文件</li>
              <li>• 重新打包应用后，环境变量才会生效</li>
              <li>• 确保网络连接正常，某些企业网络可能阻止API请求</li>
              <li>• 查看开发者工具控制台获取详细错误信息</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DebugPanel
