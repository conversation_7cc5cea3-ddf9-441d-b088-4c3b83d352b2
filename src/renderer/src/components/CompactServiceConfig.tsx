/**
 * 🎨 紧凑美观的AI服务配置界面
 * 适合实际窗口大小，可滚动，支持自定义API
 */

import React, { useState, useEffect } from 'react'
import { X, Sparkles, Settings, Mic, Brain, Star, CheckCircle, Clock, Globe, Zap, Shield, Plus } from 'lucide-react'

// 简化的自定义API管理组件
function CustomAPIManagerComponent({ onAPIChange, onClose }: { onAPIChange: (apis: CustomAPIConfig[]) => void, onClose: () => void }) {
  const [apis, setApis] = useState<CustomAPIConfig[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingAPI, setEditingAPI] = useState<CustomAPIConfig | null>(null)
  const [testingAPI, setTestingAPI] = useState<string | null>(null)
  const [testResult, setTestResult] = useState<{id: string, result: any} | null>(null)
  const [showTestForm, setShowTestForm] = useState<string | null>(null)
  const [testQuestion, setTestQuestion] = useState('你好，请介绍一下你自己。')
  const [newAPI, setNewAPI] = useState({
    name: '',
    description: '',
    icon: '🚀',
    apiType: 'openai' as const,
    baseUrl: '',
    apiKey: '',
    model: 'gpt-3.5-turbo'
  })

  // 常用模型列表
  const commonModels = {
    openai: [
      'gpt-4o',
      'gpt-4o-mini',
      'gpt-4-turbo',
      'gpt-4',
      'gpt-3.5-turbo'
    ],
    custom: [
      'Qwen3-Coder-480B-A35B-Instruct',
      'gemini-2.5-flash',
      'gemini-2.5-pro',
      'glm-z1-flash',
      'claude-3-5-sonnet-20241022',
      'claude-3-5-haiku-20241022',
      'llama-3.1-8b-instant',
      'llama-3.1-70b-versatile',
      'mixtral-8x7b-32768'
    ]
  }

  useEffect(() => {
    loadAPIs()
  }, [])

  const loadAPIs = async () => {
    try {
      const result = await window.geekAssistant?.customAPI?.getAll()
      if (result) {
        setApis(result)
        onAPIChange(result)
      }
    } catch (error) {
      console.error('Failed to load custom APIs:', error)
    }
  }

  const handleAddAPI = async () => {
    if (!newAPI.name || !newAPI.baseUrl || !newAPI.apiKey) {
      alert('请填写必要的字段：名称、API地址、API密钥')
      return
    }

    try {
      const id = await window.geekAssistant?.customAPI?.add(newAPI)
      if (id) {
        await loadAPIs()
        setShowAddForm(false)
        setNewAPI({
          name: '',
          description: '',
          icon: '🚀',
          apiType: 'openai',
          baseUrl: '',
          apiKey: '',
          model: 'gpt-3.5-turbo'
        })
      }
    } catch (error) {
      console.error('Failed to add custom API:', error)
      alert('添加失败，请检查配置')
    }
  }

  const handleEditAPI = (api: CustomAPIConfig) => {
    setEditingAPI(api)
    setNewAPI({
      name: api.name,
      description: api.description || '',
      icon: api.icon || '🚀',
      apiType: api.apiType,
      baseUrl: api.baseUrl,
      apiKey: api.apiKey,
      model: api.model || 'gpt-3.5-turbo'
    })
    setShowAddForm(true)
  }

  const handleUpdateAPI = async () => {
    if (!editingAPI || !newAPI.name || !newAPI.baseUrl || !newAPI.apiKey) {
      alert('请填写必要的字段：名称、API地址、API密钥')
      return
    }

    try {
      const success = await window.geekAssistant?.customAPI?.update(editingAPI.id, {
        name: newAPI.name,
        description: newAPI.description,
        icon: newAPI.icon,
        apiType: newAPI.apiType,
        baseUrl: newAPI.baseUrl,
        apiKey: newAPI.apiKey,
        model: newAPI.model
      })

      if (success) {
        await loadAPIs()
        setShowAddForm(false)
        setEditingAPI(null)
        setNewAPI({
          name: '',
          description: '',
          icon: '🚀',
          apiType: 'openai',
          baseUrl: '',
          apiKey: '',
          model: 'gpt-3.5-turbo'
        })
      }
    } catch (error) {
      console.error('Failed to update custom API:', error)
      alert('更新失败，请检查配置')
    }
  }

  const handleDeleteAPI = async (id: string) => {
    if (confirm('确定要删除这个自定义API吗？')) {
      try {
        await window.geekAssistant?.customAPI?.remove(id)
        await loadAPIs()
      } catch (error) {
        console.error('Failed to delete custom API:', error)
      }
    }
  }

  const handleCancelEdit = () => {
    setShowAddForm(false)
    setEditingAPI(null)
    setNewAPI({
      name: '',
      description: '',
      icon: '🚀',
      apiType: 'openai',
      baseUrl: '',
      apiKey: '',
      model: 'gpt-3.5-turbo'
    })
  }

  const handleTestAPI = async (id: string, question?: string) => {
    const testMsg = question || testQuestion
    if (!testMsg.trim()) {
      alert('请输入测试问题')
      return
    }

    setTestingAPI(id)
    setTestResult(null)

    try {
      // 通过主进程调用API，避免CORS问题
      const result = await window.geekAssistant?.customAPI?.testChat(id, testMsg)
      setTestResult({ id, result })
    } catch (error) {
      console.error('Failed to test API:', error)
      setTestResult({
        id,
        result: {
          success: false,
          message: `测试失败: ${error instanceof Error ? error.message : '未知错误'}`
        }
      })
    } finally {
      setTestingAPI(null)
    }
  }

  const handleToggleActive = async (id: string, isActive: boolean) => {
    try {
      const success = await window.geekAssistant?.customAPI?.update(id, { isActive })
      if (success) {
        await loadAPIs()
      }
    } catch (error) {
      console.error('Failed to toggle API active status:', error)
    }
  }

  return (
    <div className="space-y-4">
      {/* 添加API按钮 */}
      <div className="flex justify-between items-center">
        <h4 className="text-sm font-medium text-gray-900">已添加的自定义API</h4>
        <button
          onClick={() => {
            if (showAddForm && editingAPI) {
              handleCancelEdit()
            } else {
              setShowAddForm(!showAddForm)
            }
          }}
          className="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1"
        >
          <Plus className="w-4 h-4" />
          <span>{editingAPI ? '取消编辑' : (showAddForm ? '取消' : '添加API')}</span>
        </button>
      </div>

      {/* 添加/编辑表单 */}
      {showAddForm && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between mb-2">
            <h5 className="text-sm font-medium text-gray-900">
              {editingAPI ? '编辑自定义API' : '添加新的自定义API'}
            </h5>
            {editingAPI && (
              <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                编辑模式
              </span>
            )}
          </div>

          <div className="grid grid-cols-2 gap-3">
            <input
              type="text"
              placeholder="API名称"
              value={newAPI.name}
              onChange={(e) => setNewAPI({...newAPI, name: e.target.value})}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:border-blue-500 focus:outline-none"
            />
            <input
              type="text"
              placeholder="描述（可选）"
              value={newAPI.description}
              onChange={(e) => setNewAPI({...newAPI, description: e.target.value})}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:border-blue-500 focus:outline-none"
            />
          </div>

          <div>
            <label className="block text-xs text-gray-600 mb-1">API地址</label>
            <input
              type="text"
              placeholder="如: http://**************:50112/v1"
              value={newAPI.baseUrl}
              onChange={(e) => setNewAPI({...newAPI, baseUrl: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:border-blue-500 focus:outline-none"
            />
          </div>

          <div>
            <label className="block text-xs text-gray-600 mb-1">API密钥</label>
            <input
              type="password"
              placeholder="sk-..."
              value={newAPI.apiKey}
              onChange={(e) => setNewAPI({...newAPI, apiKey: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:border-blue-500 focus:outline-none"
            />
          </div>

          <div>
            <label className="block text-xs text-gray-600 mb-1">模型选择</label>
            <div className="space-y-2">
              <select
                value={newAPI.model}
                onChange={(e) => setNewAPI({...newAPI, model: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:border-blue-500 focus:outline-none"
              >
                <option value="">选择模型...</option>
                <optgroup label="OpenAI 模型">
                  {commonModels.openai.map(model => (
                    <option key={model} value={model}>{model}</option>
                  ))}
                </optgroup>
                <optgroup label="其他常用模型">
                  {commonModels.custom.map(model => (
                    <option key={model} value={model}>{model}</option>
                  ))}
                </optgroup>
              </select>

              <div className="text-xs text-gray-500">
                或者手动输入模型名称：
              </div>
              <input
                type="text"
                placeholder="输入自定义模型名称..."
                value={newAPI.model}
                onChange={(e) => setNewAPI({...newAPI, model: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:border-blue-500 focus:outline-none"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <button
              onClick={handleCancelEdit}
              className="px-3 py-1.5 text-gray-600 text-sm hover:text-gray-800 transition-colors"
            >
              取消
            </button>
            <button
              onClick={editingAPI ? handleUpdateAPI : handleAddAPI}
              className="px-3 py-1.5 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
            >
              {editingAPI ? '保存更改' : '添加'}
            </button>
          </div>
        </div>
      )}

      {/* API列表 */}
      <div className="space-y-2">
        {apis.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="text-2xl mb-2">🎯</div>
            <p className="text-sm">还没有自定义API</p>
          </div>
        ) : (
          apis.map((api) => (
            <div key={api.id} className="bg-white border border-gray-200 rounded-lg p-4 space-y-3">
              {/* API基本信息 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{api.icon}</span>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h5 className="font-medium text-sm text-gray-900">{api.name}</h5>
                      {api.isActive && (
                        <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">
                          激活中
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-600">{api.description || '自定义API服务'}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {/* 激活/停用切换 */}
                  <button
                    onClick={() => handleToggleActive(api.id, !api.isActive)}
                    className={`text-xs px-2 py-1 rounded transition-colors ${
                      api.isActive
                        ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50'
                        : 'text-green-600 hover:text-green-800 hover:bg-green-50'
                    }`}
                  >
                    {api.isActive ? '停用' : '激活'}
                  </button>

                  {/* 测试按钮 */}
                  <button
                    onClick={() => setShowTestForm(showTestForm === api.id ? null : api.id)}
                    disabled={testingAPI === api.id}
                    className="text-purple-600 hover:text-purple-800 text-sm px-2 py-1 rounded hover:bg-purple-50 transition-colors disabled:opacity-50"
                  >
                    {testingAPI === api.id ? '测试中...' : (showTestForm === api.id ? '收起测试' : '测试问答')}
                  </button>

                  <button
                    onClick={() => handleEditAPI(api)}
                    className="text-blue-600 hover:text-blue-800 text-sm px-2 py-1 rounded hover:bg-blue-50 transition-colors"
                  >
                    编辑
                  </button>
                  <button
                    onClick={() => handleDeleteAPI(api.id)}
                    className="text-red-600 hover:text-red-800 text-sm px-2 py-1 rounded hover:bg-red-50 transition-colors"
                  >
                    删除
                  </button>
                </div>
              </div>

              {/* API详细信息 */}
              <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                <div className="grid grid-cols-1 gap-2 text-xs">
                  <div>
                    <span className="text-gray-500 font-medium">API地址:</span>
                    <div className="text-gray-900 font-mono bg-white px-2 py-1 rounded border mt-1 break-all">
                      {api.baseUrl}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-500 font-medium">API密钥:</span>
                    <div className="text-gray-900 font-mono bg-white px-2 py-1 rounded border mt-1 break-all text-xs">
                      {api.apiKey}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-500 font-medium">类型:</span>
                      <div className="text-gray-900">{api.apiType.toUpperCase()}</div>
                    </div>
                    <div>
                      <span className="text-gray-500 font-medium">模型:</span>
                      <div className="text-gray-900">{api.model || 'default'}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 内联测试表单 */}
              {showTestForm === api.id && (
                <div className="mt-3 p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <h6 className="text-sm font-medium text-purple-800 mb-2">测试API问答</h6>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs text-purple-700 mb-1">测试问题</label>
                      <textarea
                        value={testQuestion}
                        onChange={(e) => setTestQuestion(e.target.value)}
                        placeholder="输入你想测试的问题..."
                        className="w-full px-2 py-1.5 border border-purple-300 rounded text-xs focus:border-purple-500 focus:outline-none resize-none"
                        rows={2}
                      />
                    </div>
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => setShowTestForm(null)}
                        className="px-2 py-1 text-purple-600 text-xs hover:text-purple-800 transition-colors"
                      >
                        取消
                      </button>
                      <button
                        onClick={() => handleTestAPI(api.id, testQuestion)}
                        disabled={testingAPI === api.id}
                        className="px-2 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 transition-colors disabled:opacity-50"
                      >
                        {testingAPI === api.id ? '测试中...' : '开始测试'}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* 测试结果显示 */}
              {testResult && testResult.id === api.id && (
                <div className={`mt-3 p-3 rounded-lg border ${
                  testResult.result.success
                    ? 'bg-green-50 border-green-200'
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <h6 className={`text-sm font-medium ${
                      testResult.result.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {testResult.result.success ? '✅ 测试成功' : '❌ 测试失败'}
                    </h6>
                    {testResult.result.responseTime && (
                      <span className="text-xs text-gray-600">
                        {testResult.result.responseTime}ms
                      </span>
                    )}
                  </div>

                  {testResult.result.success && testResult.result.question && (
                    <div className="space-y-2 text-xs">
                      <div>
                        <span className="font-medium text-gray-700">问题:</span>
                        <div className="text-gray-900 bg-white p-2 rounded border mt-1">
                          {testResult.result.question}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">回答:</span>
                        <div className="text-gray-900 bg-white p-2 rounded border mt-1 max-h-32 overflow-y-auto">
                          {testResult.result.answer}
                        </div>
                      </div>
                    </div>
                  )}

                  {!testResult.result.success && (
                    <div className="text-xs text-red-700">
                      {testResult.result.message}
                    </div>
                  )}
                </div>
              )}
            </div>
          ))
        )}
      </div>


    </div>
  )
}

interface CustomAPIConfig {
  id: string
  name: string
  description?: string
  icon?: string
  apiType: 'openai' | 'claude' | 'custom'
  baseUrl: string
  apiKey: string
  compatibility?: 'strict' | 'loose'
  headers?: Record<string, string>
  isActive?: boolean
  model?: string
  temperature?: number
  maxTokens?: number
}

interface CompactServiceConfigProps {
  onClose?: () => void
}

export function CompactServiceConfig({ onClose }: CompactServiceConfigProps) {
  const [selectedMode, setSelectedMode] = useState<'gemini-live' | 'separated'>('separated')
  const [selectedTranscription, setSelectedTranscription] = useState('deepgram')
  const [selectedAI, setSelectedAI] = useState('groq')
  const [customAPIs, setCustomAPIs] = useState<CustomAPIConfig[]>([])
  const [showCustomAPIManager, setShowCustomAPIManager] = useState(false)

  // 🔧 修复：从后端加载保存的配置和自定义API
  useEffect(() => {
    const loadConfig = async () => {
      try {
        console.log('🔧 CompactServiceConfig: Starting to load config...')

        // 从后端加载配置
        if (window.geekAssistant?.getCurrentServiceConfig) {
          const config = await window.geekAssistant.getCurrentServiceConfig()

          if (config) {
            console.log('🔧 CompactServiceConfig: Successfully loaded config:', JSON.stringify(config, null, 2))

            if (config.mode === 'separated' && config.separated) {
              console.log('🔧 CompactServiceConfig: Setting separated mode with:', {
                transcription: config.separated.transcription.provider,
                ai: config.separated.ai.provider
              })
              setSelectedMode('separated')
              setSelectedTranscription(config.separated.transcription.provider)
              setSelectedAI(config.separated.ai.provider)
            } else if (config.mode === 'gemini-live') {
              console.log('🔧 CompactServiceConfig: Setting gemini-live mode')
              setSelectedMode('gemini-live')
            }
          } else {
            console.log('🔧 CompactServiceConfig: No config found, using defaults')
          }
        } else {
          console.error('🔧 CompactServiceConfig: window.geekAssistant.getCurrentServiceConfig not available')
        }
      } catch (error) {
        console.error('🔧 CompactServiceConfig: Failed to load config:', error)
      }
    }

    loadConfig()
    // 加载自定义API
    loadCustomAPIs()

    // 🔧 修复：监听配置更新事件
    const handleConfigUpdate = (event: CustomEvent) => {
      console.log('🔧 CompactServiceConfig: Received config update:', event.detail)
      const config = event.detail

      if (config.mode === 'separated' && config.separated) {
        setSelectedMode('separated')
        setSelectedTranscription(config.separated.transcription.provider)
        setSelectedAI(config.separated.ai.provider)
      } else if (config.mode === 'gemini-live') {
        setSelectedMode('gemini-live')
      }
    }

    window.addEventListener('service-config-updated', handleConfigUpdate as EventListener)

    return () => {
      window.removeEventListener('service-config-updated', handleConfigUpdate as EventListener)
    }
  }, [])

  const loadCustomAPIs = async () => {
    try {
      const result = await window.geekAssistant?.customAPI?.getAll()
      if (result) {
        setCustomAPIs(result.filter(api => api.isActive))
      }
    } catch (error) {
      console.error('Failed to load custom APIs:', error)
    }
  }

  const handleConfigChange = async (config: any) => {
    try {
      console.log('🔧 CompactServiceConfig: Updating service configuration:', config)

      // 🔧 修复：移除localStorage重复检查，统一使用后端配置管理

      // 🐾 修复：构建完整的配置格式并保存到 localStorage
      let fullConfig: any

      if (config.mode === 'separated') {
        fullConfig = {
          mode: 'separated',
          separated: {
            transcription: {
              provider: config.transcription,
              config: {
                apiKey: config.transcription === 'speechmatics' ? 'hEMM081yWFGCKLSvSWs1Tox6FGk4PLn1' :
                        config.transcription === 'gladia' ? 'da495f4d-893b-4aac-beb2-c3c313c143fe' :
                        config.transcription === 'deepgram' ? '****************************************' :
                        '715199c92f02499481dc8574f93e8e04',
                language: 'zh-CN', // 通用语言代码，各Provider内部转换
                model: config.transcription === 'deepgram' ? 'nova-2' : undefined,
                realtime: true
              }
            },
            ai: {
              provider: config.ai.startsWith('custom-') ? 'custom' : config.ai,
              config: {
                apiKey: config.ai.startsWith('custom-') ? '' : '********************************************************',
                model: config.ai === 'groq' ? 'llama-3.1-8b-instant' : 'gpt-3.5-turbo',
                temperature: 0.7,
                maxTokens: 500,
                customId: config.ai.startsWith('custom-') ? config.ai : undefined
              }
            }
          }
        }
      } else {
        fullConfig = {
          mode: 'gemini-live',
          geminiLive: {
            apiKey: 'AIzaSyDxcxP-FViBZOUw6s2Obsji5lllDS1QOiw',
            model: 'gemini-live-2.5-flash-preview',
            language: 'cmn-CN',
            customPrompt: '',
            profile: 'interview'
          }
        }
      }

      // 🔧 修复：移除localStorage保存，统一使用后端配置管理
      console.log('🔧 CompactServiceConfig: Preparing to save config:', JSON.stringify(fullConfig, null, 2))

      // 通知主进程
      if (window.geekAssistant?.updateServiceConfig) {
        console.log('🔧 CompactServiceConfig: Notifying main process with:', JSON.stringify(fullConfig, null, 2))
        const result = await window.geekAssistant.updateServiceConfig(fullConfig)
        console.log('🔧 CompactServiceConfig: Main process response:', result)

        // 配置保存成功，立即更新UI状态
        if (result) {
          console.log('🔧 CompactServiceConfig: Configuration saved successfully')

          // 🔧 修复：立即更新本地UI状态，确保显示正确
          if (fullConfig.mode === 'separated' && fullConfig.separated) {
            setSelectedMode('separated')
            setSelectedTranscription(fullConfig.separated.transcription.provider)
            setSelectedAI(fullConfig.separated.ai.provider)
            console.log('🔧 CompactServiceConfig: Updated UI to separated mode:', {
              transcription: fullConfig.separated.transcription.provider,
              ai: fullConfig.separated.ai.provider
            })
          } else if (fullConfig.mode === 'gemini-live') {
            setSelectedMode('gemini-live')
            console.log('🔧 CompactServiceConfig: Updated UI to gemini-live mode')
          }

          // 🔧 立即触发前端重新读取配置
          if (window.geekAssistant?.getCurrentServiceConfig) {
            window.geekAssistant.getCurrentServiceConfig().then(newConfig => {
              console.log('🔧 立即更新前端配置显示:', newConfig)
              // 触发自定义事件通知前端更新
              window.dispatchEvent(new CustomEvent('service-config-updated', { detail: newConfig }))
            })
          }
        } else {
          console.error('🔧 CompactServiceConfig: Configuration save failed')
        }
      } else {
        console.warn('🔧 CompactServiceConfig: window.geekAssistant.updateServiceConfig not available')
      }
    } catch (error) {
    }
  }

  const handleModeChange = (mode: 'gemini-live' | 'separated') => {
    setSelectedMode(mode)
    if (mode === 'gemini-live') {
      handleConfigChange({ mode: 'gemini-live' })
    } else {
      handleConfigChange({
        mode: 'separated',
        transcription: selectedTranscription,
        ai: selectedAI
      })
    }
  }

  const handleServiceChange = (type: 'transcription' | 'ai', serviceId: string) => {
    console.log('🔧 CompactServiceConfig: Service change triggered:', { type, serviceId })

    if (type === 'transcription') {
      setSelectedTranscription(serviceId)
    } else {
      setSelectedAI(serviceId)
    }

    const newConfig = {
      mode: 'separated',
      transcription: type === 'transcription' ? serviceId : selectedTranscription,
      ai: type === 'ai' ? serviceId : selectedAI
    }

    console.log('🔧 CompactServiceConfig: Calling handleConfigChange with:', newConfig)
    handleConfigChange(newConfig)
  }

  return (
    <div className="fixed inset-0 z-50 bg-white">
      {/* 顶部导航栏 - 固定 */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Settings className="w-4 h-4 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-gray-900">AI服务配置</h1>
            <p className="text-xs text-gray-600">选择最适合的服务方案</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
        >
          <X className="w-4 h-4 text-gray-600" />
        </button>
      </div>

      {/* 可滚动内容区域 */}
      <div className="h-[calc(100vh-64px)] overflow-y-auto">
        <div className="p-4 space-y-6">
          {/* 服务模式选择 - 紧凑版 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {/* Gemini Live */}
            <div
              className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-200 p-4 ${
                selectedMode === 'gemini-live'
                  ? 'bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg'
                  : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
              }`}
              onClick={() => handleModeChange('gemini-live')}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Sparkles className="w-5 h-5" />
                  <div>
                    <h3 className="font-semibold text-sm">Google Gemini Live</h3>
                    <p className={`text-xs ${selectedMode === 'gemini-live' ? 'text-white/80' : 'text-gray-600'}`}>
                      一体化实时语音AI
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  {selectedMode === 'gemini-live' && <CheckCircle className="w-4 h-4 text-green-300" />}
                  <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${
                    selectedMode === 'gemini-live'
                      ? 'bg-yellow-400 text-yellow-900'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    <Star className="w-3 h-3" />
                    <span>推荐</span>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className={selectedMode === 'gemini-live' ? 'text-white/70' : 'text-gray-500'}>延迟</span>
                  <div className="font-medium text-green-400">&lt; 500ms</div>
                </div>
                <div>
                  <span className={selectedMode === 'gemini-live' ? 'text-white/70' : 'text-gray-500'}>准确率</span>
                  <div className="font-medium text-blue-400">95%+</div>
                </div>
              </div>
            </div>

            {/* 分离式服务 */}
            <div
              className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-200 p-4 ${
                selectedMode === 'separated'
                  ? 'bg-gradient-to-br from-green-500 to-teal-600 text-white shadow-lg'
                  : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
              }`}
              onClick={() => handleModeChange('separated')}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Settings className="w-5 h-5" />
                  <div>
                    <h3 className="font-semibold text-sm">分离式服务</h3>
                    <p className={`text-xs ${selectedMode === 'separated' ? 'text-white/80' : 'text-gray-600'}`}>
                      自由搭配转录和AI
                    </p>
                  </div>
                </div>
                {selectedMode === 'separated' && <CheckCircle className="w-4 h-4 text-green-300" />}
              </div>
              
              <div className="text-xs">
                <span className={selectedMode === 'separated' ? 'text-white/70' : 'text-gray-500'}>当前组合</span>
                <div className="font-medium">
                  {selectedTranscription === 'speechmatics' ? 'Speechmatics' :
                   selectedTranscription === 'gladia' ? 'Gladia' :
                   selectedTranscription === 'deepgram' ? 'Deepgram' :
                   selectedTranscription === 'assemblyai' ? 'AssemblyAI' : 'Google Speech'} + {selectedAI === 'groq' ? 'Groq' : selectedAI === 'together' ? 'Together AI' : 'OpenAI'}
                </div>
              </div>
            </div>
          </div>

          {/* 分离式服务详细配置 */}
          {selectedMode === 'separated' && (
            <div className="space-y-6">
              {/* 转录服务 */}
              <div>
                <div className="flex items-center space-x-2 mb-3">
                  <Mic className="w-4 h-4 text-blue-600" />
                  <h2 className="text-sm font-semibold text-gray-900">转录服务</h2>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {[
                    { id: 'speechmatics', name: 'Speechmatics', icon: '🎙️', quota: '8小时/月', latency: '250ms', accuracy: '96%', recommended: true },
                    { id: 'gladia', name: 'Gladia', icon: '🎵', quota: '10小时/月', latency: '300ms', accuracy: '94%', recommended: true },
                    { id: 'deepgram', name: 'Deepgram', icon: '🌊', quota: '$200免费', latency: '300ms', accuracy: '95%', recommended: true },
                    { id: 'assemblyai', name: 'AssemblyAI', icon: '🎯', quota: '5小时/月', latency: '500ms', accuracy: '92%' },
                    { id: 'google', name: 'Google Speech', icon: '🎤', quota: '60分钟/月', latency: '400ms', accuracy: '94%' }
                  ].map((service) => (
                    <div
                      key={service.id}
                      className={`rounded-lg cursor-pointer transition-all duration-200 p-3 ${
                        selectedTranscription === service.id
                          ? 'bg-blue-500 text-white shadow-md'
                          : 'bg-white border border-gray-200 hover:border-blue-300'
                      }`}
                      onClick={() => handleServiceChange('transcription', service.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{service.icon}</span>
                          <div>
                            <h3 className="font-medium text-sm">{service.name}</h3>
                            <p className={`text-xs ${
                              selectedTranscription === service.id ? 'text-white/80' : 'text-gray-600'
                            }`}>{service.quota}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          {service.recommended && (
                            <div className={`px-1.5 py-0.5 rounded text-xs ${
                              selectedTranscription === service.id
                                ? 'bg-yellow-400 text-yellow-900'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>推荐</div>
                          )}
                          {selectedTranscription === service.id && <CheckCircle className="w-4 h-4 text-green-300" />}
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className={selectedTranscription === service.id ? 'text-white/70' : 'text-gray-500'}>延迟</span>
                          <div className="font-medium">&lt; {service.latency}</div>
                        </div>
                        <div>
                          <span className={selectedTranscription === service.id ? 'text-white/70' : 'text-gray-500'}>准确率</span>
                          <div className="font-medium">{service.accuracy}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* AI服务 */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <Brain className="w-4 h-4 text-purple-600" />
                    <h2 className="text-sm font-semibold text-gray-900">AI服务</h2>
                  </div>
                  <button
                    onClick={() => setShowCustomAPIManager(true)}
                    className="flex items-center space-x-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                  >
                    <Plus className="w-3 h-3" />
                    <span>管理自定义API</span>
                  </button>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                  {/* 内置AI服务 */}
                  {[
                    { id: 'groq', name: 'Groq', icon: '⚡', quota: '14.4K请求/天', latency: '100ms', quality: '90%', recommended: true },
                    { id: 'together', name: 'Together AI', icon: '🤝', quota: '$25免费', latency: '800ms', quality: '92%' },
                    { id: 'openai', name: 'OpenAI GPT', icon: '🧠', quota: '需付费', latency: '1000ms', quality: '95%' }
                  ].map((service) => (
                    <div
                      key={service.id}
                      className={`rounded-lg cursor-pointer transition-all duration-200 p-3 ${
                        selectedAI === service.id
                          ? 'bg-purple-500 text-white shadow-md'
                          : 'bg-white border border-gray-200 hover:border-purple-300'
                      }`}
                      onClick={() => handleServiceChange('ai', service.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{service.icon}</span>
                          <div>
                            <h3 className="font-medium text-sm">{service.name}</h3>
                            <p className={`text-xs ${
                              selectedAI === service.id ? 'text-white/80' : 'text-gray-600'
                            }`}>{service.quota}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          {service.recommended && (
                            <div className={`px-1.5 py-0.5 rounded text-xs ${
                              selectedAI === service.id
                                ? 'bg-yellow-400 text-yellow-900'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>推荐</div>
                          )}
                          {selectedAI === service.id && <CheckCircle className="w-4 h-4 text-green-300" />}
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className={selectedAI === service.id ? 'text-white/70' : 'text-gray-500'}>延迟</span>
                          <div className="font-medium">&lt; {service.latency}</div>
                        </div>
                        <div>
                          <span className={selectedAI === service.id ? 'text-white/70' : 'text-gray-500'}>质量</span>
                          <div className="font-medium">{service.quality}</div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* 自定义API服务 */}
                  {customAPIs.map((api) => (
                    <div
                      key={api.id}
                      className={`rounded-lg cursor-pointer transition-all duration-200 p-3 ${
                        selectedAI === api.id
                          ? 'bg-gradient-to-br from-purple-500 to-pink-600 text-white shadow-md'
                          : 'bg-white border border-gray-200 hover:border-purple-300'
                      }`}
                      onClick={() => handleServiceChange('ai', api.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{api.icon || '🚀'}</span>
                          <div>
                            <h3 className="font-medium text-sm">{api.name}</h3>
                            <p className={`text-xs ${
                              selectedAI === api.id ? 'text-white/80' : 'text-gray-600'
                            }`}>{api.description || '自定义API'}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <div className={`px-1.5 py-0.5 rounded text-xs ${
                            selectedAI === api.id
                              ? 'bg-pink-400 text-pink-900'
                              : 'bg-pink-100 text-pink-800'
                          }`}>自定义</div>
                          {selectedAI === api.id && <CheckCircle className="w-4 h-4 text-green-300" />}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className={selectedAI === api.id ? 'text-white/70' : 'text-gray-500'}>模型</span>
                          <div className="font-medium">{api.model || 'Custom'}</div>
                        </div>
                        <div>
                          <span className={selectedAI === api.id ? 'text-white/70' : 'text-gray-500'}>类型</span>
                          <div className="font-medium">{api.apiType.toUpperCase()}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 自定义API管理弹窗 */}
          {showCustomAPIManager && (
            <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
              <div className="bg-white rounded-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
                <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">自定义API管理</h3>
                  <button
                    onClick={() => setShowCustomAPIManager(false)}
                    className="w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                  >
                    <X className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
                <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
                  <CustomAPIManagerComponent
                    onAPIChange={(apis) => {
                      setCustomAPIs(apis.filter(api => api.isActive))
                    }}
                    onClose={() => setShowCustomAPIManager(false)}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CompactServiceConfig
