import React, { useEffect, useRef, useCallback } from 'react'
import { ArrowLeft, Volume2, RefreshCw, Wifi, WifiOff, Trash2, Globe, Send } from 'lucide-react'
import PermissionsSetup from './PermissionsSetup'
import ErrorMessage from './ErrorMessage'
import { ErrorType } from '../utils/ErrorHandler'
import { cacheUtils } from '../utils/LocalStorageCache'
import { eventUtils } from '../utils/EventListenerManager'
import { errorUtils } from '../utils/ErrorHandler'
import { poolUtils, ServiceConfig } from '../utils/ServiceAdapterPool'
import { useOptimizedObjectState } from '../hooks/useOptimizedState'
import NetworkStatus from "@/components/NetworkStatus.tsx";
import { AIThinkingAnimation, FadeIn, SlideIn, MessageBubble } from './OptimizedAnimations'
// networkUtils和uiUtils导入已优化移除（未在组件中使用）

// 全局类型已在 src/types/window.d.ts 声明，此处无需重复声明

// 多语言文本
const texts = {
  zh: {
    backToMain: '返回主页',
    liveInterview: 'Live Interview',
    aiResponse: 'AI 助手回复',
    clear: '清空',
    diagnose: '诊断',
    transcription: '实时转录',
    waitingForConversation: '等待开始对话',
    noTranscriptionContent: '暂无转录内容',
    connecting: '连接中...',
    connected: '已连接',
    ready: '准备就绪',
    disconnected: '连接已断开',
    apiKeyNotConfigured: 'API密钥未配置',
    apiConnectionFailed: 'API连接失败',
    connectionFailed: '连接失败',
    audioCaptureFailed: '音频捕获失败',
    initializationFailed: '初始化失败',
    audioPermissionRequired: '需要屏幕录制权限'
  },
  en: {
    backToMain: 'Back to Main',
    liveInterview: 'Live Interview',
    aiResponse: 'AI Assistant Response',
    clear: 'Clear',
    diagnose: 'Diagnose',
    transcription: 'Live Transcription',
    waitingForConversation: 'Waiting for conversation',
    noTranscriptionContent: 'No transcription content',
    connecting: 'Connecting...',
    connected: 'Connected',
    ready: 'Ready',
    disconnected: 'Connection lost',
    apiKeyNotConfigured: 'API key not configured',
    apiConnectionFailed: 'API connection failed',
    connectionFailed: 'Connection failed',
    audioCaptureFailed: 'Audio capture failed',
    initializationFailed: 'Initialization failed',
    audioPermissionRequired: 'Screen recording permission required'
  }
}

// 构建增强的提示词
function buildEnhancedPrompt(customPrompt: string, aiLanguage: string, programmingLanguage: string, jobType: string): string {
  const languageMap: { [key: string]: string } = {
    'chinese': '中文',
    'english': 'English'
  }
  
  const jobTypeMap: { [key: string]: string } = {
    'backend': '后端开发',
    'frontend': '前端开发',
    'fullstack': '全栈开发',
    'mobile': '移动开发',
    'devops': 'DevOps工程师',
    'data': '数据工程师',
    'ai': 'AI工程师',
    'qa': '测试工程师',
    'product': '产品经理',
    'design': 'UI/UX设计师'
  }

  const responseLanguage = languageMap[aiLanguage] || '中文'
  const jobTitle = jobTypeMap[jobType] || '软件工程师'

  // 基础角色：面试候选人视角
  let enhancedPrompt = `你是一名经验丰富的 ${jobTitle} 候选人，请使用 ${responseLanguage} 与面试官交流，并给出专业、条理清晰的回答。`

  // 编程语言 / 技术栈额外上下文
  if (programmingLanguage && programmingLanguage !== 'none') {
    enhancedPrompt += ` 目标技术栈为 ${programmingLanguage}。`
  }

  // 回答要求
  enhancedPrompt += ` 回答要求：\n1. 结合自身经历展示深度与广度；\n2. 结构化表达（总分总或分点阐述）；\n3. 需要时给出示例代码或场景；\n4. 保持自信和谦逊的语气。`

  // 用户额外补充
  if (customPrompt) {
    enhancedPrompt += `\n\n额外要求：${customPrompt}`
  }

  return enhancedPrompt
}

interface CollaborationModeProps {
  onBack: () => void
}

// 🆕 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: any) {
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="h-screen bg-white flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600 mb-4">组件发生错误</h2>
            <p className="text-gray-600 mb-4">前端组件崩溃，请查看控制台日志</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              重新加载
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

function CollaborationModeInner({ onBack }: CollaborationModeProps) {
  // 🐾🔄 第9步优化：使用优化的状态管理系统
  const {
    state: appState,
    updateProperty,
    updateProperties,
    updatePropertyThrottled,
    getPerformance
  } = useOptimizedObjectState({
    // 基础状态
    transcription: '',
    aiResponse: '',
    isConnected: false,
    status: '',
    currentServiceMode: 'gemini-live' as 'separated' | 'gemini-live',
    language: (() => {
      // 获取UI语言设置
      const cached = cacheUtils.get('geek-assistant-ui-language', () => localStorage.getItem('geek-assistant-ui-language'))
      return (cached as 'zh' | 'en') || 'zh'
    })(),
    isAIThinking: false,
    isInitializing: false,
    isListening: false,

    // 🎯 方案H：转录状态指示
    transcriptionStatus: {
      type: 'none',
      confidence: 0,
      isStable: false,
      timestamp: 0
    },

    // 权限和错误处理状态
    showPermissionsSetup: false,
    currentError: null as {type: ErrorType, message: string} | null,
    currentErrorId: null as string | null,

    // 🐾🔄 服务适配器连接池状态（第5步优化）
    currentAdapterId: null as string | null,
    currentServiceConfig: null as any,

    // 🐾🎨 第11步优化：UI性能监控状态
    showPerformancePanel: false,
    showUIDemo: false,

    // 内存监控已优化移除
    isServiceSwitching: false
  }, {
    enableBatching: true,
    debounceDelay: 16, // 一帧时间
    enablePerformanceTracking: true
  })

  // 内存管理已简化

  // 保持aiResponseRef用于快速访问
  const aiResponseRef = useRef(appState.aiResponse)

  // 🐾🔄 第9步优化：音频错误抑制机制
  const lastAudioErrorRef = useRef<number>(0)
  const AUDIO_ERROR_COOLDOWN = 30000 // 30秒内不重复显示相同音频错误

  // 🐾🔄 同步aiResponseRef（第9步优化）
  useEffect(() => {
    aiResponseRef.current = appState.aiResponse
  }, [appState.aiResponse])

  const t = texts[appState.language]

  // 获取当前服务显示名称
  const getServiceDisplayName = () => {
    if (appState.currentServiceConfig && appState.currentServiceConfig.separated) {
      const transcription = appState.currentServiceConfig.separated.transcription.provider
      const ai = appState.currentServiceConfig.separated.ai.provider

      // 服务名称映射
      const serviceNames: Record<string, string> = {
        deepgram: 'Deepgram',
        speechmatics: 'Speechmatics',
        gladia: 'Gladia',
        assemblyai: 'AssemblyAI',
        google: 'Google Speech',
        groq: 'Groq',
        together: 'TogetherAI',
        openai: 'OpenAI',
        claude: 'Claude',
        ollama: 'Ollama'
      }

      const transcriptionName = serviceNames[transcription] || transcription
      const aiName = serviceNames[ai] || ai

      return `${transcriptionName}+${aiName}`
    }
    return 'Deepgram+Groq' // 默认显示
  }

  // 🐾🔄 优化的状态更新函数（第9步优化）
  const updateTranscription = useCallback((text: string) => {
    updatePropertyThrottled('transcription', text)
  }, [updatePropertyThrottled])

  const updateAIResponse = useCallback((response: string) => {
    updateProperty('aiResponse', response)
    updateProperty('isAIThinking', false)
  }, [updateProperty])

  const updateError = useCallback((error: {type: ErrorType, message: string} | null, errorId: string | null = null) => {
    updateProperties({
      currentError: error,
      currentErrorId: errorId
    })
  }, [updateProperties])

  // 🐾🔄 批量状态更新辅助函数（第9步优化）
  const updateServiceState = useCallback((updates: {
    isConnected?: boolean
    status?: string
    isInitializing?: boolean
    isListening?: boolean
    isServiceSwitching?: boolean
    currentError?: {type: ErrorType, message: string} | null
    currentServiceMode?: 'separated' | 'gemini-live'
    currentAdapterId?: string | null
  }) => {
    updateProperties(updates)
  }, [updateProperties])

  // 性能监控功能已优化移除

  // 设置本地化状态
  const setLocalizedStatus = useCallback((statusKey: keyof typeof texts.zh) => {
    updateProperty('status', t[statusKey])
  }, [updateProperty, t])

  // 解析用户配置，兼容多种格式
  const parseUserConfig = (savedConfig: any) => {
    if (!savedConfig) return null

    // 检查 Gemini Live 配置
    if (savedConfig.mode === 'gemini-live') {
      return {
        mode: 'gemini-live',
        geminiLive: savedConfig.geminiLive
      }
    }

    // 格式1：完整的 ServiceConfig 格式 { mode: 'separated', separated: {...} }
    if (savedConfig.mode === 'separated' && savedConfig.separated) {
      return {
        mode: 'separated',
        transcription: savedConfig.separated.transcription,
        ai: savedConfig.separated.ai
      }
    }

    // 格式2：简化格式 { transcription: {...}, ai: {...} }
    if (savedConfig.transcription && savedConfig.ai) {
      return {
        mode: 'separated',
        transcription: savedConfig.transcription,
        ai: savedConfig.ai
      }
    }

    // 格式3：更简化的格式，只有 provider 信息
    if (savedConfig.transcription?.provider && savedConfig.ai?.provider) {
      return {
        mode: 'separated',
        transcription: savedConfig.transcription,
        ai: savedConfig.ai
      }
    }

    return null
  }

  // 🐾🔄 第9步优化：智能AI服务初始化（自动降级机制）
  const initializeAIService = async () => {
    // 🆕 防止重复初始化
    if (appState.isInitializing) {
      return
    }

    try {
      updateServiceState({
        isInitializing: true
      })
      setLocalizedStatus('connecting')

      // 🐾 修复：根据用户配置选择正确的服务

      // 检查用户配置
      let userConfig: any = null
      let parsedUserConfig: any = null

      // 🔧 不再使用 localStorage，统一从后端读取配置
      console.log('🔧 统一配置：不再使用localStorage，直接从ServiceConfigManager读取')

      // 🔧 移除基于localStorage的配置分支，统一使用后端配置

      // 🔧 统一使用ServiceConfigManager的配置文件，增加重试机制
      let currentConfig = null
      let retryCount = 0
      const maxRetries = 3

      while (retryCount < maxRetries && !currentConfig) {
        try {
          currentConfig = await window.geekAssistant?.getCurrentServiceConfig?.()
          if (currentConfig) {
            console.log('🔄 Using unified config from ServiceConfigManager:', currentConfig.mode)
            // 🔧 立即更新前端显示的配置
            updateProperty('currentServiceConfig', currentConfig)

            // 🔧 监听配置更新事件（来自主进程和前端）
            const handleConfigUpdate = (event: CustomEvent) => {
              console.log('🔧 收到配置更新事件:', event.detail)
              updateProperty('currentServiceConfig', event.detail)
            }
            window.addEventListener('service-config-updated', handleConfigUpdate as EventListener)

            // 🔧 监听来自主进程的配置更新
            const handleMainProcessConfigUpdate = (_event: any, config: any) => {
              console.log('🔧 收到主进程配置更新:', config)
              updateProperty('currentServiceConfig', config)
              window.dispatchEvent(new CustomEvent('service-config-updated', { detail: config }))
            }
            window.geekAssistant?.on?.('service-config-updated', handleMainProcessConfigUpdate)

            if (currentConfig.mode === 'separated') {
              // 🔧 修复：传递用户配置到分离式服务初始化
              await initializeSeparatedService(currentConfig.separated)
              return
            } else if (currentConfig.mode === 'gemini-live') {
              await initializeGeminiLive()
              return
            }
            break
          }
        } catch (error) {
          console.log(`Failed to get unified config, retry ${retryCount + 1}/${maxRetries}:`, error)
          retryCount++
          if (retryCount < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒后重试
          }
        }
      }

      // 🔧 修复：如果所有重试都失败，显示错误而不是静默降级
      if (!currentConfig) {
        updateError({
          type: 'service-error',
          message: '无法加载服务配置，请检查设置页面的API配置是否正确'
        })
        setLocalizedStatus('connectionFailed')
        return
      }

      // 如果配置模式不明确，降级到分离式服务
      console.log('🔧 Config mode unclear, falling back to separated service')
      await initializeSeparatedService()
    } catch (error) {
      updateError({
        type: 'unknown-error',
        message: `初始化失败: ${error}`
      })
      setLocalizedStatus('initializationFailed')
    } finally {
      // 🆕 重置初始化状态
      updateProperty('isInitializing', false)
      // 🐾📊 性能监控：记录AI服务初始化完成（第4步优化）
    }
  }

  // 🐾🤖 初始化 Gemini Live 服务
  const initializeGeminiLive = async () => {

    updateServiceState({
      currentServiceMode: 'gemini-live',
      isConnected: false,
      currentError: null
    })

    // 🐾 修复：使用默认配置，避免未定义的函数
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY || 'AIzaSyDxcxP-FViBZOUw6s2Obsji5lllDS1QOiw'

    if (!apiKey) {
      throw new Error('Gemini API密钥未配置')
    }

    // 创建 Gemini Live 配置
    const geminiConfig: ServiceConfig = {
      mode: 'gemini-live',
      geminiLive: {
        apiKey,
        model: 'gemini-live-2.5-flash-preview',
        language: 'cmn-CN',
        customPrompt: '你是一个专业的面试助手，请帮助用户进行面试练习。',
        profile: 'interview',
        timeout: 30000,
        retryAttempts: 3
      }
    }

    // 切换后端服务
    const switchResult = await window.geekAssistant?.switchService?.(geminiConfig)
    if (!switchResult?.success) {
      throw new Error(`Gemini Live 服务切换失败: ${switchResult?.error || 'Unknown error'}`)
    }

    // 获取适配器
    const adapterId = await poolUtils.getAdapter(geminiConfig)

    updateServiceState({
      currentAdapterId: adapterId,
      currentServiceMode: 'gemini-live',
      isConnected: true,
      currentError: null
    })
    updateProperty('currentServiceConfig', geminiConfig)
    setLocalizedStatus('connected')

    // 启动音频捕获
    const audioSuccess = await (window.geekAssistant?.startAudioCapture?.() ?? Promise.resolve(false))

    if (audioSuccess) {
      updateServiceState({
        isListening: true
      })
      setLocalizedStatus('ready')
    }
  }

  // 🐾🔗 初始化分离式服务
  const initializeSeparatedService = async (userConfig?: any) => {
    // 🆕 防止重复的服务切换
    if (appState.isServiceSwitching) {
      console.log('Service switching already in progress, ignoring duplicate request')
      return
    }

    // 🔧 检查当前服务状态，如果已经是separated模式且连接正常，则跳过初始化
    try {
      const currentStatus = await window.geekAssistant?.getServiceStatus?.()
      if (currentStatus?.status === 'connected') {
        const currentConfig = await window.geekAssistant?.getCurrentServiceConfig?.()
        if (currentConfig?.mode === 'separated') {
          console.log('Separated service already connected, skipping initialization')
          updateServiceState({
            currentServiceMode: 'separated',
            isConnected: true,
            currentError: null,
            isServiceSwitching: false
          })
          setLocalizedStatus('ready')

          // 启动音频捕获
          const audioResult = await (window.geekAssistant?.startAudioCapture?.() ?? Promise.resolve(false))
          if (audioResult) {
            updateServiceState({
              isListening: true
            })
          }
          return
        }
      }
    } catch (error) {
      console.log('Failed to check current service status, proceeding with initialization')
    }

    updateServiceState({
      currentServiceMode: 'separated',
      isConnected: false,
      currentError: null,
      isServiceSwitching: true
    })

    let separatedConfig: ServiceConfig

    // 🔧 修复：优先使用传入的用户配置
    if (userConfig && userConfig.transcription && userConfig.ai) {
      console.log('🔧 Using user config for separated service:', userConfig)

      separatedConfig = {
        mode: 'separated',
        separated: userConfig
      }
    } else {
      // 使用默认配置
      separatedConfig = {
        mode: 'separated',
        separated: {
          transcription: {
            provider: 'deepgram',
            config: {
              apiKey: 'b35ec484a76dade61b4554f38a95a6ad33e8fa1a',
              language: 'zh',
              model: 'nova-2',
              realtime: true
            }
          },
          ai: {
            provider: 'groq',
            config: {
              apiKey: '********************************************************',
              model: 'llama-3.1-8b-instant',
              temperature: 0.7,
              maxTokens: 500
            }
          }
        }
      }
    }

    updateProperty('currentServiceConfig', separatedConfig)

    try {
      // 🐾 关键：切换后端服务
      const switchResult = await window.geekAssistant?.switchService?.(separatedConfig)

      if (!switchResult?.success) {
        throw new Error(`分离式服务切换失败: ${switchResult?.error || 'Unknown error'}`)
      }

      // 获取适配器
      const adapterId = await poolUtils.getAdapter(separatedConfig)

      updateProperty('currentAdapterId', adapterId)
      updateServiceState({
        currentServiceMode: 'separated',
        isConnected: true,
        currentError: null,
        isServiceSwitching: false
      })
      setLocalizedStatus('ready')

      // 启动音频捕获
      const audioResult = await (window.geekAssistant?.startAudioCapture?.() ?? Promise.resolve(false))

      if (audioResult) {
        updateServiceState({
          isListening: true
        })
        setLocalizedStatus('ready')
      } else {
      }

    } catch (error) {
      updateServiceState({
        currentError: {
          type: 'service-error',
          message: `分离式服务初始化失败: ${error}`
        },
        isServiceSwitching: false
      })
      setLocalizedStatus('connectionFailed')
    }
  }

  // 权限处理
  const handlePermissionsComplete = useCallback(() => {
    updateProperties({
      showPermissionsSetup: false,
      currentError: null
    })
    initializeAIService()
  }, [updateProperties])

  const handlePermissionsSkip = useCallback(() => {
    updateProperty('showPermissionsSetup', false)
    initializeAIService()
  }, [updateProperty])

  // 重启音频捕获
  const handleRestartAudio = async () => {
    try {
      const success = await (window.geekAssistant?.restartAudioCapture?.() ?? Promise.resolve(false))
      if (success) {
        updateServiceState({
          isListening: true,
          currentError: null
        })
      } else {
        // 🐾🔄 第9步优化：音频重启失败时的友好提示
        updateError({
          type: 'audio-device-error',
          message: '音频重启失败，请检查屏幕录制权限'
        })

        // 3秒后自动清除错误提示
        setTimeout(() => {
          updateError(null)
        }, 3000)
      }
    } catch (error) {
      // 🐾🔄 第9步优化：异常情况下的友好提示
      updateError({
        type: 'audio-device-error',
        message: '音频服务异常，请稍后重试'
      })

      // 3秒后自动清除错误提示
      setTimeout(() => {
        updateError(null)
      }, 3000)
    }
  }

  // 🐾👂 手动切换到Gemini Live的函数（第2步优化）
  const switchToGeminiLive = async () => {

    // 智能预加载已优化

    updateServiceState({
      currentError: null,
      isInitializing: true
    })
    setLocalizedStatus('connecting')

    try {
      // 🔍 详细的API密钥检查
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY
      const assemblyAIKey = import.meta.env.VITE_ASSEMBLYAI_API_KEY
      const deepgramKey = import.meta.env.VITE_DEEPGRAM_API_KEY
      const groqKey = import.meta.env.VITE_GROQ_API_KEY

      console.log('🔑 API密钥检查:', {
        gemini: !!apiKey,
        assemblyAI: !!assemblyAIKey,
        deepgram: !!deepgramKey,
        groq: !!groqKey
      })

      if (!apiKey) {
        // 🐾🛡️ 使用统一错误处理系统（第3步优化）
        await errorUtils.handle({
          type: 'configuration-error',
          message: `Gemini API密钥未配置！请检查：
          1. .env.local 文件中是否有 VITE_GEMINI_API_KEY
          2. 是否重新打包了应用
          3. 打开调试面板查看详细信息`,
          component: 'CollaborationMode.switchToGeminiLive',
          recoverable: false
        })
        setLocalizedStatus('apiKeyNotConfigured')
        updateProperty('isInitializing', false)
        return
      }

      // 检查转录服务API密钥
      if (!assemblyAIKey && !deepgramKey) {
        console.warn('⚠️ 没有配置转录服务API密钥，转录功能可能无法正常工作')
        await errorUtils.handle({
          type: 'configuration-error',
          message: `转录服务API密钥未配置！请在 .env.local 中配置：
          - VITE_ASSEMBLYAI_API_KEY 或
          - VITE_DEEPGRAM_API_KEY
          然后重新打包应用`,
          component: 'CollaborationMode.switchToGeminiLive',
          recoverable: false
        })
      }

      // 🐾💾 使用缓存系统获取配置参数
      const customPrompt = cacheUtils.get('geek-assistant-selected-preparation', () => localStorage.getItem('geek-assistant-selected-preparation')) || ''
      const purpose = cacheUtils.getSelectedPurpose() || 'interview'
      const aiLanguage = cacheUtils.getSelectedAILanguage() || 'chinese'
      const programmingLanguage = cacheUtils.getSelectedProgrammingLanguage() || 'java'
      const jobType = cacheUtils.getSelectedJobType() || 'backend'
      let speechLanguage = cacheUtils.getSelectedLanguage() || 'cmn-CN'

      // 修复旧的语言代码
      if (speechLanguage === 'zh-CN') {
        speechLanguage = 'cmn-CN'
      }

      // 构建增强的提示词
      const enhancedPrompt = buildEnhancedPrompt(customPrompt, aiLanguage, programmingLanguage, jobType)

      // 🐾🔄 使用ServiceAdapterPool初始化Gemini Live（第5步优化）
      const geminiConfig: ServiceConfig = {
        mode: 'gemini-live',
        geminiLive: {
          apiKey,
          model: 'gemini-live-2.5-flash-preview',
          language: speechLanguage,
          customPrompt: enhancedPrompt,
          profile: purpose,
          timeout: 30000,
          retryAttempts: 3
        }
      }

      // 🐾 修复：先切换后端ServiceManager的配置
      const switchResult = await window.geekAssistant?.switchService?.(geminiConfig)
      if (!switchResult?.success) {
        throw new Error(`服务切换失败: ${switchResult?.error || 'Unknown error'}`)
      }

      const adapterId = await poolUtils.getAdapter(geminiConfig)

      updateServiceState({
        currentAdapterId: adapterId,
        currentServiceMode: 'gemini-live',
        isConnected: true,
        currentError: null
      })
      setLocalizedStatus('connected')

      // 启动音频捕获
      const audioSuccess = await (window.geekAssistant?.startAudioCapture?.() ?? Promise.resolve(false))

      if (audioSuccess) {
        // 音频处理优化已启用

        setLocalizedStatus('ready')
        updateProperty('isListening', true)
      } else {
        // 🐾🛡️ 使用统一错误处理系统（第3步优化）
        await errorUtils.handle({
          type: 'audio-error',
          message: '无法启动系统音频捕获，请检查屏幕录制权限',
          component: 'CollaborationMode.switchToGeminiLive',
          recoverable: true
        })
        setLocalizedStatus('audioCaptureFailed')
      }
    } catch (error) {
      // 🐾🛡️ 使用统一错误处理系统（第3步优化）
      await errorUtils.handle({
        type: 'unknown-error',
        message: `切换失败: ${error}`,
        details: error,
        component: 'CollaborationMode.switchToGeminiLive',
        recoverable: true
      })
      setLocalizedStatus('connectionFailed')
    } finally {
      updateProperty('isInitializing', false)
    }
  }

  // 清空内容
  const handleClear = useCallback(() => {
    updateProperties({
      transcription: '',
      aiResponse: '',
      isAIThinking: false,
      currentError: null,
      currentErrorId: null
    })
    // 🐾🛡️ 清空错误状态（第3步优化）
    errorUtils.clear()
    aiResponseRef.current = ''
  }, [updateProperties])



  // 🐾🛡️ 错误重试功能（第3步优化）
  const handleRetryError = async () => {
    const { currentErrorId } = appState
    if (currentErrorId) {
      const success = await errorUtils.retry(currentErrorId)
      if (success) {
        updateError(null, null)
        // 重新初始化服务
        await initializeAIService()
      } else {
      }
    }
  }

  // 语言切换
  const toggleLanguage = useCallback(() => {
    const newLanguage = appState.language === 'zh' ? 'en' : 'zh'
    updateProperty('language', newLanguage)

    // 语言切换记录已优化

    // 🐾💾 使用缓存系统保存UI语言设置（第1步优化）
    cacheUtils.set('geek-assistant-ui-language', newLanguage)
  }, [appState.language, updateProperty])

  // 组件挂载时初始化
  useEffect(() => {
    // 🐾📊 性能监控：记录组件初始化开始（第4步优化）


    // 🆕 只在未初始化时才初始化
    if (!appState.isInitializing && !appState.isConnected) {
      initializeAIService().finally(() => {
        // 🐾📊 性能监控：记录组件初始化完成（第4步优化）
        // 只在开发环境输出性能日志
        if (process.env.NODE_ENV === 'development') {
        }
      })
    }

    // 🐾🔄 组件卸载时清理连接池（第5步优化）
    return () => {
      if (appState.currentAdapterId) {
        poolUtils.releaseAdapter(appState.currentAdapterId).catch(error => {
        })
      }
      // 记录组件卸载
    }
  }, []) // 🆕 确保依赖数组为空，只在挂载时执行一次

  // 🐾👂 使用EventListenerManager统一管理事件监听器（第2步优化）
  useEffect(() => {
    // 🐾 修复：确保window.geekAssistant可用后再设置事件监听器
    if (!window.geekAssistant || !window.geekAssistant.onTranscriptionUpdate || !window.geekAssistant.onAIResponse) {
      return
    }

    // 🐾 修复：等待服务初始化完成后再设置监听器
    if (appState.isInitializing) {
      return
    }


    const listenerIds: string[] = []

    try {
      // 设置转录监听器
      if (window.geekAssistant?.onTranscriptionUpdate) {
        const cleanupTranscription = window.geekAssistant.onTranscriptionUpdate((data: string) => {
          try {
            console.log('🎯 前端收到转录更新:', data)
            updateTranscription(data)

            if (data.trim() && aiResponseRef.current.trim() === '') {
              // 仅当前没有 AI 回复内容时才进入思考状态，避免收到 AI 回复后又被覆盖
              updateProperty('isAIThinking', true)
            }
            console.log('🎯 前端转录处理完成')
          } catch (error) {
            console.error('转录更新错误:', error)
          }
        })

        // 使用EventListenerManager管理清理函数
        const listenerId = `transcription_${Date.now()}`
        eventUtils.addCleanupCallback(listenerId, cleanupTranscription)
        listenerIds.push(listenerId)
      }

      // 🎯 方案H：添加转录状态监听器
      if (window.geekAssistant?.on) {
        const cleanupTranscriptionStatus = window.geekAssistant.on('transcription-status', (status: any) => {
          try {
            // 更新转录状态指示
            updateProperty('transcriptionStatus', {
              type: status.type,
              confidence: status.confidence,
              isStable: status.isStable,
              timestamp: Date.now()
            })
          } catch (error) {
            console.error('转录状态更新错误:', error)
          }
        })

        const statusListenerId = `transcription_status_${Date.now()}`
        eventUtils.addCleanupCallback(statusListenerId, cleanupTranscriptionStatus)
        listenerIds.push(statusListenerId)
      }

      // 设置AI回复监听器
      if (window.geekAssistant?.onAIResponse) {
        const cleanupAIResponse = window.geekAssistant.onAIResponse((data: string) => {
          try {
            // 立即更新状态
            aiResponseRef.current = data
            updateAIResponse(data)
          } catch (error) {
            console.error('AI回复更新错误:', error)
          }
        })

        // 使用EventListenerManager管理清理函数
        const listenerId = `ai_response_${Date.now()}`
        eventUtils.addCleanupCallback(listenerId, cleanupAIResponse)
        listenerIds.push(listenerId)
      }

      // 设置状态更新监听器
      if (window.geekAssistant?.onStatusUpdate) {
        const cleanupStatusUpdate = window.geekAssistant.onStatusUpdate((data: string) => {
          updateProperty('status', data)
        })

        // 使用EventListenerManager管理清理函数
        const listenerId = `status_update_${Date.now()}`
        eventUtils.addCleanupCallback(listenerId, cleanupStatusUpdate)
        listenerIds.push(listenerId)
      } else {
      }


      // 诊断函数已优化移除

    } catch (error) {
    }

    return () => {
      try {
        // 使用EventListenerManager批量清理
        eventUtils.removeBatch(listenerIds)
      } catch (error) {
      }
    }
  }, [appState.isInitializing, appState.isConnected, appState.currentServiceMode]) // 🐾 修复：依赖服务状态，确保监听器正确设置

  return (
    <div className="h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 flex flex-col animate-in fade-in duration-500">
      {/* 权限设置向导 */}
      <PermissionsSetup
        isOpen={appState.showPermissionsSetup}
        onComplete={handlePermissionsComplete}
        onSkip={handlePermissionsSkip}
      />

      {/* 错误消息 */}
      {appState.currentError && (
        <ErrorMessage
          type={appState.currentError.type}
          message={appState.currentError.message}
          onDismiss={() => updateError(null)}
          onFix={handleRetryError}
        />
      )}

      {/* 彩色顶部导航栏 */}
      <div className="flex items-center justify-between p-4 border-b border-purple-200/50 bg-white/80 backdrop-blur-sm shadow-lg">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-all duration-200 hover:scale-105 p-2 rounded-lg hover:bg-purple-50"
          >
            <ArrowLeft className="w-5 h-5" />
            <span className="font-medium">{t.backToMain}</span>
          </button>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg animate-pulse">
              <span className="text-white text-lg">🤖</span>
            </div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">{t.liveInterview}</h1>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* 彩色动态服务模式显示 */}
          <div className={`flex items-center space-x-2 px-3 py-2 text-xs rounded-full shadow-md transition-all duration-200 hover:scale-105 ${
            appState.currentServiceMode === 'separated'
              ? 'bg-gradient-to-r from-green-400 to-emerald-400 text-white'
              : 'bg-gradient-to-r from-blue-400 to-indigo-400 text-white'
          }`}>
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span className="font-medium">
              {appState.currentServiceMode === 'separated' ?
                `🔗 ${getServiceDisplayName()}` :
                '🤖 Gemini Live'
              }
            </span>
          </div>

          {/* 彩色语言切换 */}
          <button
            onClick={toggleLanguage}
            className="flex items-center space-x-2 px-3 py-2 text-xs rounded-full border-2 border-purple-200 bg-white/80 hover:bg-purple-50 hover:border-purple-300 transition-all duration-200 hover:scale-105 shadow-md"
          >
            <Globe className="w-3 h-3 text-purple-600" />
            <span className="font-medium text-purple-700">{appState.language.toUpperCase()}</span>
          </button>



          {/* 内存监控按钮已移除以优化性能 */}

          {/* 连接状态 */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {appState.isConnected ? (
                <Wifi className="w-4 h-4 text-green-500" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-500" />
              )}
              <span className="text-sm text-gray-600">{appState.status}</span>
            </div>

            {/* 🐾🌐 第10步优化：网络状态显示 */}
            <NetworkStatus className="text-sm" />
          </div>
        </div>
      </div>

      {/* 彩色主要内容区域 */}
      <div className="flex-1 flex p-6 space-x-6 overflow-hidden">
        {/* 左侧：AI回复区域 */}
        <div className="flex-1 flex flex-col">
          <div className="bg-white/80 backdrop-blur-sm border-2 border-purple-200/50 rounded-2xl p-6 flex-1 shadow-xl hover:shadow-2xl transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-bold text-gray-900 flex items-center group-hover:text-purple-600 transition-colors duration-200">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mr-3 shadow-lg group-hover:scale-110 transition-transform duration-200">
                  <Send className="w-4 h-4 text-white" />
                </div>
                {t.aiResponse}
              </h2>
              <div className="flex space-x-2">
                <button
                  onClick={handleClear}
                  className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 hover:text-white bg-gray-100 hover:bg-gradient-to-r hover:from-red-400 hover:to-pink-400 rounded-full transition-all duration-200 hover:scale-105 shadow-md hover:shadow-lg"
                >
                  <Trash2 className="w-4 h-4" />
                  <span className="font-medium">{t.clear}</span>
                </button>

                <button
                  onClick={handleRestartAudio}
                  className="flex items-center space-x-1 px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>重启音频</span>
                </button>
                {appState.currentServiceMode === 'separated' && (
                  <button
                    onClick={switchToGeminiLive}
                    className="flex items-center space-x-1 px-3 py-1 text-sm text-green-600 hover:text-green-800 hover:bg-green-50 rounded transition-colors"
                  >
                    <span>🤖</span>
                    <span>切换到Gemini</span>
                  </button>
                )}
              </div>
            </div>

            <div className="h-full overflow-y-auto p-2">
              {appState.isAIThinking ? (
                <FadeIn>
                  <AIThinkingAnimation className="p-4" />
                </FadeIn>
              ) : appState.aiResponse ? (
                <SlideIn direction="up">
                  <MessageBubble type="ai" className="w-full max-w-none">
                    <div className="w-full">
                      <p className="text-gray-800 leading-relaxed whitespace-pre-wrap break-words text-sm">{appState.aiResponse}</p>
                    </div>
                  </MessageBubble>
                </SlideIn>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Volume2 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>{t.waitingForConversation}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 右侧：转录区域 */}
        <div className="w-80 flex flex-col">
          <div className="bg-white/80 backdrop-blur-sm border-2 border-blue-200/50 rounded-2xl p-4 flex-1 shadow-xl hover:shadow-2xl transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-bold text-gray-900 flex items-center group-hover:text-blue-600 transition-colors duration-200">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mr-3 shadow-lg group-hover:scale-110 transition-transform duration-200">
                  <Volume2 className="w-4 h-4 text-white" />
                </div>
                {t.transcription}
              </h2>
              <div className="flex items-center space-x-2">
                <div className={`text-xs rounded-full px-3 py-1 shadow-md transition-all duration-200 hover:scale-105 ${
                  appState.currentServiceMode === 'separated'
                    ? 'bg-gradient-to-r from-green-400 to-emerald-400 text-white'
                    : 'bg-gradient-to-r from-blue-400 to-cyan-400 text-white'
                }`}>
                  <span className="font-medium">
                    {appState.currentServiceMode === 'separated' ? getServiceDisplayName() : 'Gemini Live'}
                  </span>
                </div>

                {/* 🎯 方案H：转录状态指示器 */}
                {appState.currentServiceMode === 'separated' && appState.transcriptionStatus.type !== 'none' && (
                  <div className="flex items-center space-x-1">
                    <div className={`w-2 h-2 rounded-full ${
                      appState.transcriptionStatus.isStable ? 'bg-green-500' :
                      appState.transcriptionStatus.confidence > 0.7 ? 'bg-yellow-500' : 'bg-red-500'
                    } ${appState.transcriptionStatus.type === 'partial' ? 'animate-pulse' : ''}`}></div>
                    <span className="text-xs text-gray-500">
                      {Math.round(appState.transcriptionStatus.confidence * 100)}%
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="h-full overflow-y-auto p-2">
              {appState.transcription ? (
                <SlideIn direction="right">
                  <MessageBubble type="user" className="w-full max-w-none">
                    <p className="text-gray-800 text-sm leading-relaxed whitespace-pre-wrap break-words">{appState.transcription}</p>
                  </MessageBubble>
                </SlideIn>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <p className="text-center text-sm">{t.noTranscriptionContent}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>



      {/* 内存监控面板已移除以优化性能 */}
    </div>
  )
}

// 🆕 内部组件结束

// 🆕 使用错误边界包装的主组件
export default function CollaborationMode({ onBack }: CollaborationModeProps) {
  return (
    <ErrorBoundary>
      <CollaborationModeInner onBack={onBack} />
    </ErrorBoundary>
  )
}
