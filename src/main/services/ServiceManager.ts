/**
 * 服务管理器
 * 统一管理所有服务适配器，提供服务切换和状态管理功能
 */

import { EventEmitter } from 'events'
import { ServiceAdapter, ServiceAdapterFactory } from './ServiceAdapter'
import { serviceConfigManager } from './config/ServiceConfig'
import { 
  ServiceConfig, 
  ServiceStatusInfo, 
  ServiceMode,
  ServiceEvent,
  ServiceEventData
} from './types/ServiceTypes'

/**
 * 服务管理器类
 */
export class ServiceManager extends EventEmitter {
  private static instance: ServiceManager
  private currentAdapter: ServiceAdapter | null = null
  private isInitialized: boolean = false
  private currentConfig: ServiceConfig | null = null

  private constructor() {
    super()
    console.log('ServiceManager created')
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager()
    }
    return ServiceManager.instance
  }

  /**
   * 初始化服务管理器
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('Initializing ServiceManager...')
      
      // 初始化配置管理器
      await serviceConfigManager.initialize()
      
      // 获取当前配置
      this.currentConfig = serviceConfigManager.getConfig()
      
      // 创建并初始化默认适配器
      const success = await this.switchToService(this.currentConfig)
      
      if (success) {
        this.isInitialized = true
        console.log('ServiceManager initialized successfully')
      } else {
        console.error('Failed to initialize default service adapter')
      }
      
      return success
      
    } catch (error) {
      console.error('Failed to initialize ServiceManager:', error)
      return false
    }
  }

  /**
   * 切换到指定服务
   * @param config 服务配置
   */
  async switchToService(config: ServiceConfig): Promise<boolean> {
    try {
      console.log('Switching to service:', config.mode)

      // 🔧 增强的重复切换检测
      if (this.currentAdapter && this.currentConfig &&
          this.currentConfig.mode === config.mode) {

        // 对于separated模式，检查具体的服务配置
        if (config.mode === 'separated' && this.currentConfig.separated && config.separated) {
          const currentTranscription = this.currentConfig.separated.transcription?.provider
          const newTranscription = config.separated.transcription?.provider
          const currentAI = this.currentConfig.separated.ai?.provider
          const newAI = config.separated.ai?.provider

          if (currentTranscription === newTranscription && currentAI === newAI) {
            console.log('Service already using the same providers, skipping switch')
            return true
          }
        } else if (config.mode === 'gemini-live') {
          console.log('Service already using gemini-live, skipping switch')
          return true
        }
      }

      // 🔧 检查适配器是否正在初始化或连接中
      if (this.currentAdapter && typeof this.currentAdapter.isInitializing === 'function' &&
          this.currentAdapter.isInitializing()) {
        console.log('Service switch already in progress, waiting for completion...')
        // 等待当前初始化完成，而不是直接拒绝
        let retries = 0
        while (this.currentAdapter.isInitializing() && retries < 10) {
          await new Promise(resolve => setTimeout(resolve, 500))
          retries++
        }
        if (retries >= 10) {
          console.warn('Service switch timeout, proceeding anyway')
        }
      }

      // 停止当前服务
      if (this.currentAdapter) {
        await this.currentAdapter.stopSession()
        await this.currentAdapter.disconnect()
        this.currentAdapter.removeAllListeners()
        this.currentAdapter = null
      }

      // 创建新的适配器
      const newAdapter = await ServiceAdapterFactory.createAdapter(config)

      // 设置事件监听
      this.setupAdapterEvents(newAdapter)

      // 初始化新适配器
      const success = await newAdapter.initialize(config)

      if (success) {
        this.currentAdapter = newAdapter
        this.currentConfig = config

        // 保存配置
        await serviceConfigManager.updateConfig(config)

        console.log('Service switched successfully to:', config.mode)
        this.emit('service-switched', config.mode)
        return true
      } else {
        console.error('Failed to initialize new service adapter')
        newAdapter.removeAllListeners()
        return false
      }

    } catch (error) {
      console.error('Failed to switch service:', error)
      return false
    }
  }

  /**
   * 启动当前服务会话
   */
  async startSession(): Promise<boolean> {
    if (!this.currentAdapter) {
      console.error('No service adapter available')
      return false
    }

    try {
      const success = await this.currentAdapter.startSession()
      if (success) {
        console.log('Service session started')
        this.emit('session-started')
      }
      return success
    } catch (error) {
      console.error('Failed to start service session:', error)
      return false
    }
  }

  /**
   * 停止当前服务会话
   */
  async stopSession(): Promise<void> {
    if (this.currentAdapter) {
      try {
        await this.currentAdapter.stopSession()
        console.log('Service session stopped')
        this.emit('session-stopped')
      } catch (error) {
        console.error('Failed to stop service session:', error)
      }
    }
  }

  /**
   * 发送音频数据
   * @param audioData Base64编码的音频数据
   */
  async sendAudio(audioData: string): Promise<void> {
    if (this.currentAdapter) {
      try {
        await this.currentAdapter.sendAudio(audioData)
      } catch (error) {
        console.error('Failed to send audio data:', error)
      }
    }
  }

  /**
   * 重新连接当前服务
   */
  async reconnect(): Promise<boolean> {
    if (!this.currentAdapter) {
      console.error('No service adapter available for reconnection')
      return false
    }

    try {
      const success = await this.currentAdapter.reconnect()
      if (success) {
        console.log('Service reconnected successfully')
        this.emit('service-reconnected')
      }
      return success
    } catch (error) {
      console.error('Failed to reconnect service:', error)
      return false
    }
  }

  /**
   * 断开当前服务
   */
  async disconnect(): Promise<void> {
    if (this.currentAdapter) {
      try {
        await this.currentAdapter.disconnect()
        console.log('Service disconnected')
        this.emit('service-disconnected')
      } catch (error) {
        console.error('Failed to disconnect service:', error)
      }
    }
  }

  /**
   * 获取当前服务状态
   */
  getServiceStatus(): ServiceStatusInfo | null {
    return this.currentAdapter ? this.currentAdapter.getStatus() : null
  }

  /**
   * 获取当前配置
   */
  getCurrentConfig(): ServiceConfig | null {
    return this.currentConfig ? { ...this.currentConfig } : null
  }

  /**
   * 获取当前适配器
   */
  getCurrentAdapter(): ServiceAdapter | null {
    return this.currentAdapter
  }

  /**
   * 获取当前服务模式
   */
  getCurrentMode(): ServiceMode | null {
    return this.currentConfig?.mode || null
  }

  /**
   * 检查服务是否就绪
   */
  isServiceReady(): boolean {
    return this.isInitialized && this.currentAdapter?.isReady() === true
  }

  /**
   * 更新服务配置
   * @param config 新配置
   */
  async updateConfig(config: Partial<ServiceConfig>): Promise<boolean> {
    console.log('🔧 ServiceManager: Updating config with:', JSON.stringify(config, null, 2))

    if (!this.currentConfig) {
      console.error('No current config to update')
      return false
    }

    // 🔧 修复：对于完整配置，直接使用；对于部分配置，才进行合并
    let newConfig: ServiceConfig
    if (config.mode && (config as ServiceConfig).separated && config.mode === 'separated') {
      // 完整的分离式配置，直接使用
      newConfig = config as ServiceConfig
      console.log('🔧 ServiceManager: Using complete separated config')
    } else if (config.mode && (config as ServiceConfig).geminiLive && config.mode === 'gemini-live') {
      // 完整的Gemini Live配置，直接使用
      newConfig = config as ServiceConfig
      console.log('🔧 ServiceManager: Using complete gemini-live config')
    } else {
      // 部分配置，进行合并
      newConfig = { ...this.currentConfig, ...config }
      console.log('🔧 ServiceManager: Merging partial config')
    }

    console.log('🔧 ServiceManager: Final config:', JSON.stringify(newConfig, null, 2))

    // 如果服务模式改变，需要切换服务
    if (config.mode && config.mode !== this.currentConfig.mode) {
      console.log('🔧 ServiceManager: Mode changed, switching service')
      return await this.switchToService(newConfig)
    }

    // 否则只更新当前适配器的配置
    if (this.currentAdapter) {
      try {
        const success = await this.currentAdapter.updateConfig(newConfig)
        if (success) {
          this.currentConfig = newConfig
          await serviceConfigManager.updateConfig(newConfig)
          console.log('🔧 ServiceManager: Service config updated successfully')
          this.emit('config-updated', newConfig)
        }
        return success
      } catch (error) {
        console.error('Failed to update service config:', error)
        return false
      }
    }

    return false
  }

  /**
   * 设置适配器事件监听
   * @param adapter 服务适配器
   */
  private setupAdapterEvents(adapter: ServiceAdapter): void {
    // 监听服务事件并转发
    adapter.on('service-event', (eventData: ServiceEventData) => {
      console.log('Service event:', eventData.event, eventData.data)
      this.emit('service-event', eventData)
      
      // 根据事件类型进行特殊处理
      switch (eventData.event) {
        case ServiceEvent.STATUS_CHANGED:
          this.emit('status-changed', eventData.data)
          break
        case ServiceEvent.TRANSCRIPTION_RECEIVED:
          this.emit('transcription-received', eventData.data)
          break
        case ServiceEvent.AI_RESPONSE_RECEIVED:
          this.emit('ai-response-received', eventData.data)
          break
        case ServiceEvent.ERROR_OCCURRED:
          this.emit('error-occurred', eventData.data)
          break
      }
    })

    // 监听适配器错误
    adapter.on('error', (error: Error) => {
      console.error('Service adapter error:', error)
      this.emit('adapter-error', error)
    })
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    console.log('Cleaning up ServiceManager...')
    
    if (this.currentAdapter) {
      await this.currentAdapter.stopSession()
      await this.currentAdapter.disconnect()
      this.currentAdapter.removeAllListeners()
      this.currentAdapter = null
    }
    
    this.removeAllListeners()
    this.isInitialized = false
    console.log('ServiceManager cleaned up')
  }
}

// 导出单例实例
export const serviceManager = ServiceManager.getInstance()
